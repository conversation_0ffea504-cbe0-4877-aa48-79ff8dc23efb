<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <title>#if($TITLE)$TITLE#else TradingVueJs Demo #end</title>
    <script src="https://unpkg.com/vue@2.6.12/dist/vue.min.js"></script>
    <script src="/js/trading-vue.min.js"></script>
    <script>
        Data = {
            "ohlcv": $OHLCV_DATA,
            "onchart": [
                // {
                //     "name": "EMA, 25",
                //     "type": "EMA",
                //     "data": [
                //         [
                //             1551924000000,
                //             3942.57474934723
                //         ],
                //         [
                //             1551927600000,
                //             3945.0766917051355
                //         ],
                //         [
                //             1551931200000,
                //             3948.193869266279
                //         ],
                //         [
                //             1551934800000,
                //             3949.486648553488
                //         ],
                //         [
                //             1551938400000,
                //             3950.3492140493736
                //         ],
                //         [
                //             1551942000000,
                //             3951.4069668148063
                //         ],
                //         [
                //             1551945600000,
                //             3951.129507829052
                //         ],
                //         [
                //             1551949200000,
                //             3950.8096252037403
                //         ],
                //         [
                //             1551952800000,
                //             3950.470423264991
                //         ],
                //         [
                //             1551956400000,
                //             3950.69577532153
                //         ]
                //     ],
                //     "settings": {}
                // },
                // {
                //     "name": "EMA, 43",
                //     "type": "EMA",
                //     "data": [
                //         [
                //             1551924000000,
                //             3925.665846224657
                //         ],
                //         [
                //             1551927600000,
                //             3927.912853214445
                //         ],
                //         [
                //             1551931200000,
                //             3930.5349962501523
                //         ],
                //         [
                //             1551934800000,
                //             3932.101587329691
                //         ],
                //         [
                //             1551938400000,
                //             3933.4015151783415
                //         ],
                //         [
                //             1551942000000,
                //             3934.796900852053
                //         ],
                //         [
                //             1551945600000,
                //             3935.3879508133236
                //         ],
                //         [
                //             1551949200000,
                //             3935.9144545809
                //         ],
                //         [
                //             1551952800000,
                //             3936.391070281768
                //         ],
                //         [
                //             1551956400000,
                //             3937.1642034507786
                //         ],
                //         [
                //             1551960000000,
                //             3938.3385578393795
                //         ]
                //     ],
                //     "settings": {}
                // }
            ],
            "offchart": [
                // {
                //     "name": "RSI, 20",
                //     "type": "RSI",
                //     "data": [
                //         [
                //             1551924000000,
                //             62.62529388585774
                //         ],
                //         [
                //             1551927600000,
                //             65.99647609094728
                //         ],
                //         [
                //             1551931200000,
                //             68.41840214950574
                //         ],
                //         [
                //             1551934800000,
                //             59.64504798458128
                //         ],
                //         [
                //             1551938400000,
                //             58.01057577815737
                //         ],
                //         [
                //             1551942000000,
                //             58.94692045228387
                //         ],
                //         [
                //             1551945600000,
                //             52.98441177039573
                //         ],
                //         [
                //             1551949200000,
                //             52.699049631062
                //         ],
                //         [
                //             1551952800000,
                //             52.49404694628025
                //         ],
                //         [
                //             1551956400000,
                //             54.76468085772987
                //         ]
                //     ],
                //     "settings": {
                //         "upper": 70,
                //         "lower": 30,
                //         "backColor": "#9b9ba316",
                //         "bandColor": "#666"
                //     }
                // }
            ]
        }
    </script>
    <style>
        html,
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .night-mode {
            position: absolute;
            top: 10px;
            right: 80px;
            color: #888;
            font: 11px -apple-system,BlinkMacSystemFont,
            Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,
            Fira Sans,Droid Sans,Helvetica Neue,
            sans-serif
        }
    </style>
</head>


<body>
<div id="app">
    <!-- index based设置为true可以让周末没有交易的时间不会出现gap -->
    <trading-vue :data="data" :width="this.width" :height="this.height"
                 :toolbar="true"
                 :index-based="true"
                 :chart-config="{TB_ICON_BRI: 1.25}"
                 :color-back="colors.colorBack"
                 :color-grid="colors.colorGrid"
                 :color-text="colors.colorText">
    </trading-vue>
    <span class="night-mode">
            <input type="checkbox" v-model="night">
            <label>NM</label>
        </span>
</div>
<script>
    app = new Vue({
        el: '#app',
        data: {
            data: new TradingVueJs.DataCube(Data),
            width: window.innerWidth,
            height: window.innerHeight,
            night: false
        },
        mounted() {
            window.addEventListener('resize', this.onResize)
            window.DataCube = this.data
        },
        methods: {
            onResize(event) {
                this.width = window.innerWidth
                this.height = window.innerHeight
            }
        },
        computed: {
            colors() {
                return this.night ? {} : {
                    colorBack: '#fff',
                    colorGrid: '#eee',
                    colorText: '#333'
                }
            },
        },
        beforeDestroy() {
            window.removeEventListener('resize', this.onResize)
        },
    })
</script>
</body>
