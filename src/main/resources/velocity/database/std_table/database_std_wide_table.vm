<!-- database_std_wide_table.vm -->

#set($page_title='页面标题')

#parse("database/std_table/database_std_wide_table_column.vm")

<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="id">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column prop="id" label="id"></el-table-column>
        <el-table-column prop="name" label="name"></el-table-column>
        <el-table-column prop="databaseId" label="databaseId"></el-table-column>
        <el-table-column prop="databaseName" label="databaseName"></el-table-column>
        <el-table-column prop="tableName" label="tableName"></el-table-column>
        <el-table-column prop="tableDataType" label="tableDataType"></el-table-column>
        <el-table-column prop="refreshFreq" label="refreshFreq"></el-table-column>
        <el-table-column prop="refreshDelayType" label="refreshDelayType"></el-table-column>
        <el-table-column prop="refreshTime" label="refreshTime"></el-table-column>
        <el-table-column prop="sourceBizSystem" label="sourceBizSystem"></el-table-column>
        <el-table-column prop="maintainer" label="maintainer"></el-table-column>
        <el-table-column prop="follower" label="follower"></el-table-column>
        <el-table-column prop="summary" label="summary"></el-table-column>
        <el-table-column prop="summaryDemo" label="summaryDemo"></el-table-column>
        <el-table-column prop="docMarkdown" label="docMarkdown"></el-table-column>
        <el-table-column prop="docHtml" label="docHtml"></el-table-column>
        <el-table-column prop="partitionColumn" label="partitionColumn"></el-table-column>
        <el-table-column prop="partitionMin" label="partitionMin"></el-table-column>
        <el-table-column prop="partitionMax" label="partitionMax"></el-table-column>
        <el-table-column prop="partitionCount" label="partitionCount"></el-table-column>
        <el-table-column prop="partitionCountRange" label="partitionCountRange"></el-table-column>
        <el-table-column prop="createTime" label="createTime"></el-table-column>
        <el-table-column prop="updateTime" label="updateTime"></el-table-column>
        <el-table-column prop="createUserId" label="createUserId"></el-table-column>
        <el-table-column prop="updateUserId" label="updateUserId"></el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="name" prop="name">
                <el-input v-model="addEditForm.name" placeholder="标准宽表名称"></el-input>
            </el-form-item>
            <el-form-item label="databaseId" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="databaseName" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名"></el-input>
            </el-form-item>
            <el-form-item label="tableName" prop="tableName">
                <el-input v-model="addEditForm.tableName" placeholder="标准宽表表名"></el-input>
            </el-form-item>
            <el-form-item label="tableDataType" prop="tableDataType">
                <el-input v-model="addEditForm.tableDataType" placeholder="表的数据类型，SNAPSHOT全量快照，FULL全量无快照"></el-input>
            </el-form-item>
            <el-form-item label="refreshFreq" prop="refreshFreq">
                <el-input v-model="addEditForm.refreshFreq" placeholder="更新频率，用秒来表达，例如按天就是86400"></el-input>
            </el-form-item>
            <el-form-item label="refreshDelayType" prop="refreshDelayType">
                <el-input v-model="addEditForm.refreshDelayType" placeholder="数据刷新延迟类型，T、T-1、T-2"></el-input>
            </el-form-item>
            <el-form-item label="refreshTime" prop="refreshTime">
                <el-input v-model="addEditForm.refreshTime" placeholder="期望的刷新时间，一般来说，只有按天刷新的才有个期望的刷新时间"></el-input>
            </el-form-item>
            <el-form-item label="sourceBizSystem" prop="sourceBizSystem">
                <el-input v-model="addEditForm.sourceBizSystem" placeholder="来源业务系统"></el-input>
            </el-form-item>
            <el-form-item label="maintainer" prop="maintainer">
                <el-input v-model="addEditForm.maintainer" placeholder="表的维护人，多个人用逗号隔开"></el-input>
            </el-form-item>
            <el-form-item label="follower" prop="follower">
                <el-input v-model="addEditForm.follower" placeholder="关注人，多个用逗号隔开"></el-input>
            </el-form-item>
            <el-form-item label="summary" prop="summary">
                <el-input v-model="addEditForm.summary" placeholder="简介，一句话介绍这张表，该表的每一行代表什么"></el-input>
            </el-form-item>
            <el-form-item label="summaryDemo" prop="summaryDemo">
                <el-input v-model="addEditForm.summaryDemo" placeholder="一个简单的例子来概要说明这张表"></el-input>
            </el-form-item>
            <el-form-item label="docMarkdown" prop="docMarkdown">
                <el-input v-model="addEditForm.docMarkdown" placeholder="宽表的文档，富文本，markdown格式"></el-input>
            </el-form-item>
            <el-form-item label="docHtml" prop="docHtml">
                <el-input v-model="addEditForm.docHtml" placeholder="宽表的文档，html格式，它和markdown对应"></el-input>
            </el-form-item>
            <el-form-item label="partitionColumn" prop="partitionColumn">
                <el-input v-model="addEditForm.partitionColumn" placeholder="分区字段"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: ''
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_std_wide_table/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_std_wide_table/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增标准宽表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_std_wide_table/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            }
        }
    })
</script>