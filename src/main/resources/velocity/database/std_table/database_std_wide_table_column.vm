
#parse("database_std_wide_table_column_enum.vm")

<style>
</style>

<script type="text/x-template" id="databaseStdWideTableColumn">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="id">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="id"></el-table-column>
            <el-table-column prop="tableId" label="tableId"></el-table-column>
            <el-table-column prop="name" label="name"></el-table-column>
            <el-table-column prop="columnName" label="columnName"></el-table-column>
            <el-table-column prop="columnType" label="columnType"></el-table-column>
            <el-table-column label="isUniqDim">
                <template slot-scope="scope">
                    <span v-text="scope.row.isUniqDim ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column label="isEnum">
                <template slot-scope="scope">
                    <span v-text="scope.row.isEnum ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column prop="distinctValueCount" label="distinctValueCount"></el-table-column>
            <el-table-column label="isContainNull">
                <template slot-scope="scope">
                    <span v-text="scope.row.isContainNull ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column prop="minValue" label="minValue"></el-table-column>
            <el-table-column prop="maxValue" label="maxValue"></el-table-column>
            <el-table-column prop="note" label="note"></el-table-column>
            <el-table-column prop="createTime" label="createTime"></el-table-column>
            <el-table-column prop="updateTime" label="updateTime"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="tableId" prop="tableId">
                    <el-input v-model="addEditForm.tableId" placeholder="表id"></el-input>
                </el-form-item>
                <el-form-item label="name" prop="name">
                    <el-input v-model="addEditForm.name" placeholder="列名称"></el-input>
                </el-form-item>
                <el-form-item label="columnName" prop="columnName">
                    <el-input v-model="addEditForm.columnName" placeholder="表中的列名"></el-input>
                </el-form-item>
                <el-form-item label="columnType" prop="columnType">
                    <el-input v-model="addEditForm.columnType" placeholder="列的数据类型"></el-input>
                </el-form-item>
                <el-form-item label="isUniqDim" prop="isUniqDim">
                    <el-input v-model="addEditForm.isUniqDim" placeholder="是否是最细的维度列"></el-input>
                </el-form-item>
                <el-form-item label="isEnum" prop="isEnum">
                    <el-input v-model="addEditForm.isEnum" placeholder="该类是否是枚举列，当是枚举列时，必须完善枚举说明"></el-input>
                </el-form-item>
                <el-form-item label="distinctValueCount" prop="distinctValueCount">
                    <el-input v-model="addEditForm.distinctValueCount" placeholder="这一列的值个数"></el-input>
                </el-form-item>
                <el-form-item label="isContainNull" prop="isContainNull">
                    <el-input v-model="addEditForm.isContainNull" placeholder="该列是否有null值"></el-input>
                </el-form-item>
                <el-form-item label="minValue" prop="minValue">
                    <el-input v-model="addEditForm.minValue" placeholder="最小值，只存前255个字符"></el-input>
                </el-form-item>
                <el-form-item label="maxValue" prop="maxValue">
                    <el-input v-model="addEditForm.maxValue" placeholder="最大值，只存前255个字符"></el-input>
                </el-form-item>
                <el-form-item label="note" prop="note">
                    <el-input v-model="addEditForm.note" placeholder="列说明"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    Vue.component('database-std-wide-table-column', {
        template: '#databaseStdWideTableColumn',
        data: function () {
            return {
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: ''
            }
        },
        props: {
            /*id: Number*/ /*组件的参数在这里定义*/
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_std_wide_table_column/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_std_wide_table_column/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增标准宽表列' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_std_wide_table_column/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            }
        }
    })
</script>