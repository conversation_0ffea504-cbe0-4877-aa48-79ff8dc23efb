/*! TradingVue.JS - v1.0.0 - Sun Dec 20 2020
    https://github.com/tvjsx/trading-vue-js
    Copyright (c) 2019 C451 Code's All Right;
    Licensed under the MIT license */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.TradingVueJs=e():t.TradingVueJs=e()}(window,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=73)}([function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},function(t,e,n){var i=n(35),r=n(36),o=n(31),s=n(37);t.exports=function(t){return i(t)||r(t)||o(t)||s()}},function(t,e){function n(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}t.exports=function(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}},function(t,e,n){var i=n(38),r=n(39),o=n(31),s=n(40);t.exports=function(t,e){return i(t)||r(t,e)||o(t,e)||s()}},function(t,e,n){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=function(t,e){var n=t[1]||"",i=t[3];if(!i)return n;if(e&&"function"==typeof btoa){var r=(s=i,a=btoa(unescape(encodeURIComponent(JSON.stringify(s)))),c="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(a),"/*# ".concat(c," */")),o=i.sources.map((function(t){return"/*# sourceURL=".concat(i.sourceRoot||"").concat(t," */")}));return[n].concat(o).concat([r]).join("\n")}var s,a,c;return[n].join("\n")}(e,t);return e[2]?"@media ".concat(e[2]," {").concat(n,"}"):n})).join("")},e.i=function(t,n,i){"string"==typeof t&&(t=[[null,t,""]]);var r={};if(i)for(var o=0;o<this.length;o++){var s=this[o][0];null!=s&&(r[s]=!0)}for(var a=0;a<t.length;a++){var c=[].concat(t[a]);i&&r[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),e.push(c))}},e}},function(t,e,n){"use strict";function i(t,e){for(var n=[],i={},r=0;r<e.length;r++){var o=e[r],s=o[0],a={id:t+":"+r,css:o[1],media:o[2],sourceMap:o[3]};i[s]?i[s].parts.push(a):n.push(i[s]={id:s,parts:[a]})}return n}n.r(e),n.d(e,"default",(function(){return p}));var r="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!r)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},s=r&&(document.head||document.getElementsByTagName("head")[0]),a=null,c=0,u=!1,h=function(){},l=null,f="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(t,e,n,r){u=n,l=r||{};var s=i(t,e);return d(s),function(e){for(var n=[],r=0;r<s.length;r++){var a=s[r];(c=o[a.id]).refs--,n.push(c)}e?d(s=i(t,e)):s=[];for(r=0;r<n.length;r++){var c;if(0===(c=n[r]).refs){for(var u=0;u<c.parts.length;u++)c.parts[u]();delete o[c.id]}}}}function d(t){for(var e=0;e<t.length;e++){var n=t[e],i=o[n.id];if(i){i.refs++;for(var r=0;r<i.parts.length;r++)i.parts[r](n.parts[r]);for(;r<n.parts.length;r++)i.parts.push(v(n.parts[r]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var s=[];for(r=0;r<n.parts.length;r++)s.push(v(n.parts[r]));o[n.id]={id:n.id,refs:1,parts:s}}}}function g(){var t=document.createElement("style");return t.type="text/css",s.appendChild(t),t}function v(t){var e,n,i=document.querySelector('style[data-vue-ssr-id~="'+t.id+'"]');if(i){if(u)return h;i.parentNode.removeChild(i)}if(f){var r=c++;i=a||(a=g()),e=y.bind(null,i,r,!1),n=y.bind(null,i,r,!0)}else i=g(),e=_.bind(null,i),n=function(){i.parentNode.removeChild(i)};return e(t),function(i){if(i){if(i.css===t.css&&i.media===t.media&&i.sourceMap===t.sourceMap)return;e(t=i)}else n()}}var A,m=(A=[],function(t,e){return A[t]=e,A.filter(Boolean).join("\n")});function y(t,e,n,i){var r=n?"":i.css;if(t.styleSheet)t.styleSheet.cssText=m(e,r);else{var o=document.createTextNode(r),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(o,s[e]):t.appendChild(o)}}function _(t,e){var n=e.css,i=e.media,r=e.sourceMap;if(i&&t.setAttribute("media",i),l.ssrId&&t.setAttribute("data-vue-ssr-id",e.id),r&&(n+="\n/*# sourceURL="+r.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}},function(t){t.exports=JSON.parse('{"extended.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAANElEQVR4nGNggABGEMEEIlhABAeI+AASF0AlHmAqA4kzKAAx8wGQuAMKwd6AoYzBAWonAwAcLwTgNfJ3RQAAAABJRU5ErkJggg==","ray.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAAMklEQVR4nGNgQAJMIIIFRHCACAEQoQAiHICYvQEkjkrwYypjAIkzwk2zAREuqIQFzD4AE3kE4BEmGggAAAAASUVORK5CYII=","segment.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAgMAAAC5h23wAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAlQTFRFAAAATU1NJCQkCxcHIQAAAAN0Uk5TAP8SmutI5AAAACxJREFUeJxjYMACGAMgNAsLdpoVKi8AVe8A1QblQlWRKt0AoULw2w1zGxoAABdiAviQhF/mAAAAAElFTkSuQmCC","add.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAH5QTFRFAAAAAAAAAAAAAAAAAAAAAAAAAAAACgoKBgYGGxsbKioqPz8/Pj4+BQUFCQkJAQEBZGRkh4eHAgICEBAQNjY2g4ODgYGBAAAAAwMDeXl5d3d3GBgYERERgICAgICANDQ0PDw8Y2NjCAgIhYWFGhoaJycnOjo6YWFhgICAdXV14Y16sQAAACp0Uk5TAAILDxIKESEnJiYoKCgTKSkpKCAnKSkFKCkpJiDl/ycpKSA2JyYpKSkpOkQ+xgAAARdJREFUeJzllNt2gyAQRTWiRsHLoDU0GpPYmMv//2BMS+sgl6Z9bM8bi73gnJkBz/sn8lcBIUHofwtG8TpJKUuTLI6cYF7QEqRKynP71VX9AkhNXVlsbMQrLLQVGyPZLsGHWgPrCxMJwHUPlXa79NBp2et5d9f3u3m1XxatQNn7SagOXCUjCjYUDuqxcWlHj4MSfw12FDJchFViRN8+1qcQoUH6lR1L1mEMEErofB6WzEUwylzomfzOQGiOJdXiWH7mQoUyMa4WXJQWOBvLFvPCGxt6FSr5kyH0qi0YddNG2/pgCsOjff4ZTizXPNwKIzl56OoGg9d9Z/+5cs6On+CFCfevFQ3ZaTycx1YMbvDdRvjkp/lHdAcPXzokxcwfDwAAAABJRU5ErkJggg==","cursor.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAgMAAAC5h23wAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAxQTFRFAAAATU1NTU1NTU1NwlMHHwAAAAR0Uk5TAOvhxbpPrUkAAAAkSURBVHicY2BgYHBggAByabxg1WoGBq2pRCk9AKUbcND43AEAufYHlSuusE4AAAAASUVORK5CYII=","display_off.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAU1QTFRFAAAAh4eHh4eHAAAAAAAAAAAAAwMDAAAAAAAAhoaGGBgYgYGBAAAAPz8/AgICg4ODCQkJhISEh4eHh4eHPj4+NjY2gYGBg4ODgYGBgYGBgoKCAQEBJycngoKChYWFEBAQg4ODCAgIKioqZGRkCgoKBQUFERERd3d3gYGBGxsbNDQ0hISEgYGBPDw8gYGBgYGBh4eHh4eHhYWFh4eHgoKChYWFgYGBgYGBg4ODhoaGg4ODYWFhgoKCBgYGdXV1goKCg4ODgYGBgICAgYGBAAAAg4ODhYWFhISEh4eHgoKChYWFOjo6goKCGhoah4eHh4eHh4eHgoKCh4eHeXl5hoaGgoKChISEgYGBgYGBgoKCY2NjgYGBgoKCh4eHgoKCgYGBhoaGg4ODhoaGhYWFh4eHgYGBhoaGhoaGhoaGg4ODgoKChISEgoKChYWFh4eHfKktUwAAAG90Uk5TACn/AhEFKA8SLCbxCigoVBNKUTYoJ/lh3PyAKSaTNiBtICYpISggKSkmJ0LEKef3lGxA8rn//+pcMSkpnCcptHPJKe0LUjnx5LzKKaMnX73hl64pLnhkzNSgKeLv17LQ+liIzaLe7PfTw5tFpz3K1fXR/gAAAgBJREFUeJzllNdXwjAUxknB0lIoCKVsGTIFQRAZ7r333nuv///R3LZ4mlDQZ/0ekp7b37n5bnITk+mfyDxv5Tir3fwjaElO5BIOKZFLJS1dQVfI0Y809TtEV+elo95RpFPWG+1go4fdQ5QybI8haaNBkM2ANbM09bnrwaPY7iFKrz7EMBdu7CHdVruXIt0M1hb+GKA3LTRKkp5lTA6Dg6xIkhaHhvQ1IlW/UCouQdJNJTRIpk1qO7+wUpcfpl537oBc7VNip3Gi/AmVPBAC1UrL6HXtSGVT+k2Yz0Focad07OMRf3P5BEbd63PFQx7HN+w61JoAm+uBlV48O/0jkLSMmtPCmQ8HwlYdykFV4/LJPp7e3hVyFdapHNehLk6PSjhSkBvwu/cFyJGIYvOyhoc1jjYQFGbygD4CWjoAMla/og3YoSw+KPhjPNoFcim4iFD+pFYA8zZ9WeYU5OBjZ3ORWyCfG03E+47kKpCIJTpGO4KP8XMgtw990xG/PBNTgmPEEXwf7P42oOdFIRAoBCtqTKL6Rcwq4Xsgh5xYC/mmSs6yJKk1YbnVeTq1NaEpmlHbmVn2EORkW2trF2ZzmHGTSUMGl1a9hp4ySRpdQ8yKGURpMmRIYg9pb1YPzg6kO79cLlE6bYFjEtv91bLEUxvhwbWwjY13BxUb9l8+mn9EX8x3Nki8ff5wAAAAAElFTkSuQmCC","display_on.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAR1QTFRFAAAAh4eHgYGBAAAAAAAAgYGBAAAAAwMDAAAAAAAAgYGBg4ODGBgYgYGBhISEAAAAPz8/AgIChoaGCQkJhYWFPj4+NjY2goKCgYGBAQEBJycngYGBgoKCEBAQCAgIhISEKioqZGRkCgoKBQUFERERd3d3gYGBg4ODgYGBGxsbNDQ0hISEgoKCgoKChYWFPDw8gYGBgYGBhoaGgoKCg4ODgoKCgYGBgoKCgoKCgoKCg4ODgoKChoaGgoKCgYGBhoaGg4ODYWFhBgYGdXV1gYGBg4ODgoKCgICAg4ODg4ODhISEAAAAg4ODOjo6gYGBGhoaeXl5goKCgYGBgoKChYWFgoKChISEgoKCY2NjgYGBg4ODgYGBgYGBg4ODgYGBo8n54AAAAF90Uk5TACn/AhH3BSgPEuhUJvFACigoLBM2KCeA6ykm+pMgIEkmKSEoICn9XCkmJ0u6nDop4sUypGuEzLZ6vmCYLZ/dLykpJynUYa8pcllCC1Ip2ycpisl1PadFsintbsPQZdi/bTW7AAAB4UlEQVR4nOWUZ1fCMBSGSSGWFiq0UDbIkr2XbBwMxS0b1P//M0xK9XSiftX7oel585zkvfcmMRj+SRhvzRRlthm/BU3Ry3TYzofTsajpIOjw2iNAjIiddehvHXSdA0mkXEEdG0fkE1DEKXmkSVqVIA6rBmsktUgAWLWHoGp30UNclbtLmwQgoyya91wPTbFy0mQXJ5zJQO6BgXRjfH0iSkX5stHIXr5r0bB/lu8syjR8rzsFbR2SpX+5J2eMP3csLtYsEY2K8BeTFuE2jaVCBw7bHOBuxq16AXmpbui3LtIfbRLUHMY2q4lcFo2WB4KA1SUAlWumNEKCzyxBKZxVHvYGaFguCBx1vM/x0IPzoqQoj5SdP4mns2cCGhBsrgj0uaeUBtzMyxQN8w4mYROTW8+r0oANp8W5mf6WQw5aCYJ2o7ymPaKMi2uVpmWM4TW6tdImgGo1bT4nK6DbbsCc0AZSdmLEFszzHrh6riVvRrNA3/9SE8QLWQu+Gjto9+gE9NBMwr9zi83gFeeFTe11zpm1CHE3HeyVCSknf3MIDcFTbfJKdbR1L4xX49L+/BoillV5uPJqkshD3JWSgpNMXP/lcrD8+hO84MnDr5YpFHv0Fe99VjJ0GBRs2H74aP6R+ACr+TFvZNAQ1wAAAABJRU5ErkJggg==","down.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAKVQTFRFAAAAg4ODgICAAAAAAAAAAAAACAgIAAAAAAAAAAAAAAAAOTk5hYWFEBAQfHx8ODg4dnZ2NDQ0XV1dGxsbKCgogICAFBQUIiIiZGRkgICAgICAFRUVAAAAgICAgICAgICAf39/Li4ugICAcHBwgoKCgICAgoKCgICAg4ODgYGBPj4+goKCgICAhISEgYGBgICAgoKCgICAgYGBgYGBf39/gICAgICAIdPQHAAAADd0Uk5TACn/KAIRIBMFDwooKyApKSknKSYmzCcmKfL7JRCUi2L3J7IpcLUrr0VbKXntNEnkMbxrUcG56CMpi50AAAFZSURBVHic5ZRpf4MgDIeFKFatWm/tfW091u7evv9Hm1Acoujm2y0vFPH5Jf+EEE37J6bblmlatv4jaBCI4rMfR0CMXtAEJ0fccgfM7tAkQHXzArdDxggmqGETGCnJWROkNlOwOqhIhKCtgbSicw1uK/dATSK0aRatIzytA8ik4XSiyJnLSm+VPxULgeyLI3uHRJH+qcB4WZGrKb4c20WwI7b3iUt74OS6XD+xZWrXUCtme0uKTvfcJ65CZFa9VOebqwXmft+oT8yF+/VymT4XeGB+Xx8L+j4gBcoFIDT+oMz6Qp93Y74pCeBpUXaLuW0rUk6r1iv3nP322ewYkgv2nZIvgpSPQDrY5wTjRJDNg9XAE/+uSXIVX812GdKEmtvR2rtWaw+5MAOuofJy79SXu9TgBl4d9DZdI0NjgyiswNCB/qk1J5Bmvp+lQOa9IJNhW4bxm6H5R+wLQYMSQXZNzbcAAAAASUVORK5CYII=","price_range.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAAIUlEQVR4nGNggAPm/w9gTA4QIQMitECEJ1yMEgLNDiAAADfgBMRu78GgAAAAAElFTkSuQmCC","price_time.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAAOklEQVR4nGNggAPm/w9gTA4QIQPEClpMQMITRHCACScQoQQihBgY9P//grKgYk5wdTACYhQHFjuAAABZFAlc4e1fcQAAAABJRU5ErkJggg==","remove.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAK5QTFRFAAAAh4eHgICAAAAAAAAAh4eHAAAAAwMDAAAAAAAAgICAGBgYAAAAPz8/AgICgICACQkJhoaGhoaGgICAPj4+NjY2gYGBg4ODgYGBAQEBJycngoKCEBAQgICAgICACAgIKioqZGRkCgoKBQUFERERd3d3gYGBGxsbNDQ0gICAPDw8YWFhBgYGdXV1gICAg4ODgICAAAAAOjo6GhoaeXl5gICAhYWFY2NjhYWFgICA9O0oCgAAADp0Uk5TACn/AhErBSgPEvEmCigowxMuMcgoJ7hWrCkmdCD6vSAmKSEoICkpJie6KSknKSkp0wspJynCMik11rrLte8AAAFwSURBVHic5ZTXkoIwFIZNAAPSpKkoRQV7Wcva3v/FFiRmEwise7t7bs7MP98k/ylJq/VPQjjKiiJrwo+gON0uxro7XiRTsRHs+voE4JjoRrf+6sD7AFTMvaDGRht9glLMUJtLqmUwD5XDCohHAmBUPQSV27GHtFK7xycBWJab5uPaR+Hlmue7GfZxHwyWFHVMQghXFgD2A8IOZtfssdNJIXcyFEaSfchzp9BuMVP+Fhvr5Qh0nGfqYTGhm3BcYFUaQBKOhMWzRqHyGFRY03ppQ5lCFZ30RloVZGQTaa3QqEt0OyrQnkSkk8I1YJkvAwPCMgY0UpbzXRZhVbosIWGbZTLNQszGMCM42FJEjWDDjIAMtp+xj6x2K+/DqNDc0r4Yc8yGl3uer2aIyT1iyd8sYSuY8cldZbVrH4zPebTvP8OMNSoedj6XzDyk3pwG98u0/ufqGu7tBW5c1PxriXFyHq5PQxXFzeDThvbmp/lH4gt6WxfZ03H8DwAAAABJRU5ErkJggg==","settings.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAW5QTFRFAAAAAAAAAAAAAAAAAAAAAAAAAAAACgoKBgYGGxsbKioqQEBAPj4+BQUFCAgIAQEBPz8/ZWVlh4eHZGRkAgICCQkJDw8PNjY2g4ODgoKCNTU1EBAQAAAAAwMDeXl5d3d3AAAAGBgYAAAAERERioqKgoKCgoKCgoKCgYGBgoKChISEhoaGNDQ0g4ODgICAgICAgICAgYGBgYGBhYWFgICAgICAPT09AAAAgYGBgICAgICAgICAgICAY2NjCAgIgICAgICAhYWFhYWFgYGBHBwcgICAhYWFGhoagYGBgYGBg4ODhoaGJycnAAAAhISEgICAg4ODPDw8AAAAgoKCgICAhISEOjo6h4eHgoKCgYGBgICAf39/gYGBgoKCgICAGBgYgYGBg4ODg4ODgICACwsLgYGBgICAgYGBgYGBgYGBgICAgYGBYWFhf39/g4ODPj4+gYGBg4ODgICAhYWFgoKCgYGBgICAgYGBgoKCdXV1T0kC9QAAAHp0Uk5TAAILDxMKESEnJiYpKSgTKSgpKSkoEyAnKSknIAYoKSkFJQEgKl94jYVvVC4nU9f/+K8pOu71KBCi3NPq/ikg0e01Nokm1UUnsZVqQSYOT9lrKRJz5lIpK12jyu+sesgnhGVLxCG55a6Um+GaKfJCKKRgKUt8ocergymDQ9knAAABsElEQVR4nOWUV1vCMBSGg1AQpBZrcVdE3KJxo4LgnuCoe4F7orjHv7doTk3bgF7rd5OnX94nZ+SkCP0TWQqsNpuVs/wI2h2FTleR2+XkHfa8YLHgKRGJSj2SN3fosvIKkVJlVXWONGrkWtEgn1zHJP1GMCs/g7XILFIUpXoTWmaKTnIImGovh72Gxqbmlta2dvgOGpsmQO0dnfhTXd3E6JH0pN1DNnr7MFE/HDsQ0qEO6Pxg9sCh4XDkGx2J6sovBD+G8eiYuo5PxLTKeLoJBZNgT2EcnjY0YYajUKsL7Fk1gcjU3PwChcYTFGorAnsRqlpa1tAVhUbdmr+6RtjIOlgbCjMBUdzc2t7ZzbJ7zAQ4p6GSfRVNwkeKLsvCg31w2JBdjlT0GDxZNzEnpcQ+xWfnFxeXVyp6Tay07gq+L/YUOoBvbomV0V8skiq//DutWfeEfJD1JPLCED4+Pb8kX986tApNQ4iqfSJT76bRzvlgBPODQXW/foYqK5lyeBeYJEL1gaoeGnwIBhjRoQ9SZgTAdEbO/9cKRfmZ+MpGPCVHQ3nBzzS4hKIkuNyh/5g+ALiAXSSas9hwAAAAAElFTkSuQmCC","time_range.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAAJElEQVR4nGNgwAsUGJhQCScQoQQihBgY9P//grKgYk4YOvACACOpBKG6Svj+AAAAAElFTkSuQmCC","trash.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZAQMAAAD+JxcgAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAZQTFRFAAAATU1NkJ+rOQAAAAJ0Uk5TAP9bkSK1AAAALUlEQVR4nGNgAIN6ENHQACX4//9gYBBgYIESYC4LkA0lPEkmGFAI5v8PILYCAHygDJxlK0RUAAAAAElFTkSuQmCC","up.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAMZQTFRFAAAAh4eHgICAAAAAAAAAAAAAAwMDAAAAAAAAGBgYAAAAPz8/AgICCQkJgICAh4eHPj4+NjY2AQEBJycnEBAQgICAgICACAgIKioqZGRkCgoKBQUFgYGBERERd3d3gYGBGxsbNDQ0gICAgYGBPDw8gYGBh4eHgICAYWFhBgYGgYGBdXV1goKCg4ODhYWFgICAgoKCAAAAhISEOjo6gICAGhoagYGBeXl5hoaGgICAY2Njg4ODgoKCgoKCgYGBgoKCg4ODgoKC64uw1gAAAEJ0Uk5TACn/AhEFKA8SJgooKBP7KignKSYg9c0gJikhKLQgKSkmJ7ywKY8s5SknlClxKTMpXwtFKe0neiku8ClKWmSbbFFjM5GHSgAAAW5JREFUeJzllGd/gjAQxk3AMFWWOHDvVa2rVbu//5cqhJWQQO3b9nkVjv/v7rnLKJX+iYS9JMuSKvwIiu3loKkZzYHXFgvBiqW1QKSWplfySzvmAyDUN50cG2X0DDLqoTKXVLJgIIXDCohHAqCzHhymeuShy/Ru8kkAhtmhWUTvW9fdEnPQaVLU0n8XF0L3kn5P6LTtZPKgNoK+RrUkcGtQ7S9TsgOxxinrkUPYD+LwLCIh7CTsWSVQqRmTuPqpitlZFLQlApXjrsYBc335wOw47ksmUSMMrgKi/gnAE/awCqNHmTUwDf5X34LlBuedsgbUsK15kPMxTIXzzvFSIdsSPBw7nGD1K+7bL3F9xStEnZhoCw71TbpL71GBBbUF1MZmZWTOi97PI3eIJn9zCEtOj0+umaOde2EszqW9/xr6rM54WFtc0vfQNak57Ibd/Jerohu3GFwYqPjVEhve2Z4cbQU1ikFsQ73z0fwj+ga3VBezGuggFQAAAABJRU5ErkJggg=="}')},function(t,e,n){t.exports=n(71)},function(t,e,n){var i;!function(r,o,s,a){"use strict";var c,u=["","webkit","Moz","MS","ms","o"],h=o.createElement("div"),l=Math.round,f=Math.abs,p=Date.now;function d(t,e,n){return setTimeout(w(t,n),e)}function g(t,e,n){return!!Array.isArray(t)&&(v(t,n[e],n),!0)}function v(t,e,n){var i;if(t)if(t.forEach)t.forEach(e,n);else if(undefined!==t.length)for(i=0;i<t.length;)e.call(n,t[i],i,t),i++;else for(i in t)t.hasOwnProperty(i)&&e.call(n,t[i],i,t)}function A(t,e,n){var i="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",o=r.console&&(r.console.warn||r.console.log);return o&&o.call(r.console,i,n),t.apply(this,arguments)}}c="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var i=arguments[n];if(null!=i)for(var r in i)i.hasOwnProperty(r)&&(e[r]=i[r])}return e}:Object.assign;var m=A((function(t,e,n){for(var i=Object.keys(e),r=0;r<i.length;)(!n||n&&undefined===t[i[r]])&&(t[i[r]]=e[i[r]]),r++;return t}),"extend","Use `assign`."),y=A((function(t,e){return m(t,e,!0)}),"merge","Use `assign`.");function _(t,e,n){var i,r=e.prototype;(i=t.prototype=Object.create(r)).constructor=t,i._super=r,n&&c(i,n)}function w(t,e){return function(){return t.apply(e,arguments)}}function b(t,e){return"function"==typeof t?t.apply(e&&e[0]||undefined,e):t}function x(t,e){return undefined===t?e:t}function k(t,e,n){v(E(e),(function(e){t.addEventListener(e,n,!1)}))}function C(t,e,n){v(E(e),(function(e){t.removeEventListener(e,n,!1)}))}function I(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function B(t,e){return t.indexOf(e)>-1}function E(t){return t.trim().split(/\s+/g)}function S(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var i=0;i<t.length;){if(n&&t[i][n]==e||!n&&t[i]===e)return i;i++}return-1}function T(t){return Array.prototype.slice.call(t,0)}function M(t,e,n){for(var i=[],r=[],o=0;o<t.length;){var s=e?t[o][e]:t[o];S(r,s)<0&&i.push(t[o]),r[o]=s,o++}return n&&(i=e?i.sort((function(t,n){return t[e]>n[e]})):i.sort()),i}function O(t,e){for(var n,i,r=e[0].toUpperCase()+e.slice(1),o=0;o<u.length;){if((i=(n=u[o])?n+r:e)in t)return i;o++}}var Q=1;function D(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||r}var G="ontouchstart"in r,Y=undefined!==O(r,"PointerEvent"),j=G&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),U=["x","y"],R=["clientX","clientY"];function F(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){b(t.options.enable,[t])&&n.handler(e)},this.init()}function L(t,e,n){var i=n.pointers.length,r=n.changedPointers.length,o=1&e&&i-r==0,s=12&e&&i-r==0;n.isFirst=!!o,n.isFinal=!!s,o&&(t.session={}),n.eventType=e,function(t,e){var n=t.session,i=e.pointers,r=i.length;n.firstInput||(n.firstInput=N(e));r>1&&!n.firstMultiple?n.firstMultiple=N(e):1===r&&(n.firstMultiple=!1);var o=n.firstInput,s=n.firstMultiple,a=s?s.center:o.center,c=e.center=P(i);e.timeStamp=p(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=W(a,c),e.distance=z(a,c),function(t,e){var n=e.center,i=t.offsetDelta||{},r=t.prevDelta||{},o=t.prevInput||{};1!==e.eventType&&4!==o.eventType||(r=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},i=t.offsetDelta={x:n.x,y:n.y});e.deltaX=r.x+(n.x-i.x),e.deltaY=r.y+(n.y-i.y)}(n,e),e.offsetDirection=H(e.deltaX,e.deltaY);var u=K(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=u.x,e.overallVelocityY=u.y,e.overallVelocity=f(u.x)>f(u.y)?u.x:u.y,e.scale=s?(h=s.pointers,l=i,z(l[0],l[1],R)/z(h[0],h[1],R)):1,e.rotation=s?function(t,e){return W(e[1],e[0],R)+W(t[1],t[0],R)}(s.pointers,i):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,i,r,o,s=t.lastInterval||e,a=e.timeStamp-s.timeStamp;if(8!=e.eventType&&(a>25||undefined===s.velocity)){var c=e.deltaX-s.deltaX,u=e.deltaY-s.deltaY,h=K(a,c,u);i=h.x,r=h.y,n=f(h.x)>f(h.y)?h.x:h.y,o=H(c,u),t.lastInterval=e}else n=s.velocity,i=s.velocityX,r=s.velocityY,o=s.direction;e.velocity=n,e.velocityX=i,e.velocityY=r,e.direction=o}(n,e);var h,l;var d=t.element;I(e.srcEvent.target,d)&&(d=e.srcEvent.target);e.target=d}(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function N(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:l(t.pointers[n].clientX),clientY:l(t.pointers[n].clientY)},n++;return{timeStamp:p(),pointers:e,center:P(e),deltaX:t.deltaX,deltaY:t.deltaY}}function P(t){var e=t.length;if(1===e)return{x:l(t[0].clientX),y:l(t[0].clientY)};for(var n=0,i=0,r=0;r<e;)n+=t[r].clientX,i+=t[r].clientY,r++;return{x:l(n/e),y:l(i/e)}}function K(t,e,n){return{x:e/t||0,y:n/t||0}}function H(t,e){return t===e?1:f(t)>=f(e)?t<0?2:4:e<0?8:16}function z(t,e,n){n||(n=U);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return Math.sqrt(i*i+r*r)}function W(t,e,n){n||(n=U);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return 180*Math.atan2(r,i)/Math.PI}F.prototype={handler:function(){},init:function(){this.evEl&&k(this.element,this.evEl,this.domHandler),this.evTarget&&k(this.target,this.evTarget,this.domHandler),this.evWin&&k(D(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&C(this.element,this.evEl,this.domHandler),this.evTarget&&C(this.target,this.evTarget,this.domHandler),this.evWin&&C(D(this.element),this.evWin,this.domHandler)}};var J={mousedown:1,mousemove:2,mouseup:4};function $(){this.evEl="mousedown",this.evWin="mousemove mouseup",this.pressed=!1,F.apply(this,arguments)}_($,F,{handler:function(t){var e=J[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:"mouse",srcEvent:t}))}});var V={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},X={2:"touch",3:"pen",4:"mouse",5:"kinect"},Z="pointerdown",q="pointermove pointerup pointercancel";function tt(){this.evEl=Z,this.evWin=q,F.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}r.MSPointerEvent&&!r.PointerEvent&&(Z="MSPointerDown",q="MSPointerMove MSPointerUp MSPointerCancel"),_(tt,F,{handler:function(t){var e=this.store,n=!1,i=t.type.toLowerCase().replace("ms",""),r=V[i],o=X[t.pointerType]||t.pointerType,s="touch"==o,a=S(e,t.pointerId,"pointerId");1&r&&(0===t.button||s)?a<0&&(e.push(t),a=e.length-1):12&r&&(n=!0),a<0||(e[a]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(a,1))}});var et={touchstart:1,touchmove:2,touchend:4,touchcancel:8};function nt(){this.evTarget="touchstart",this.evWin="touchstart touchmove touchend touchcancel",this.started=!1,F.apply(this,arguments)}function it(t,e){var n=T(t.touches),i=T(t.changedTouches);return 12&e&&(n=M(n.concat(i),"identifier",!0)),[n,i]}_(nt,F,{handler:function(t){var e=et[t.type];if(1===e&&(this.started=!0),this.started){var n=it.call(this,t,e);12&e&&n[0].length-n[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:"touch",srcEvent:t})}}});var rt={touchstart:1,touchmove:2,touchend:4,touchcancel:8};function ot(){this.evTarget="touchstart touchmove touchend touchcancel",this.targetIds={},F.apply(this,arguments)}function st(t,e){var n=T(t.touches),i=this.targetIds;if(3&e&&1===n.length)return i[n[0].identifier]=!0,[n,n];var r,o,s=T(t.changedTouches),a=[],c=this.target;if(o=n.filter((function(t){return I(t.target,c)})),1===e)for(r=0;r<o.length;)i[o[r].identifier]=!0,r++;for(r=0;r<s.length;)i[s[r].identifier]&&a.push(s[r]),12&e&&delete i[s[r].identifier],r++;return a.length?[M(o.concat(a),"identifier",!0),a]:void 0}_(ot,F,{handler:function(t){var e=rt[t.type],n=st.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:"touch",srcEvent:t})}});function at(){F.apply(this,arguments);var t=w(this.handler,this);this.touch=new ot(this.manager,t),this.mouse=new $(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function ct(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,ut.call(this,e)):12&t&&ut.call(this,e)}function ut(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY};this.lastTouches.push(n);var i=this.lastTouches;setTimeout((function(){var t=i.indexOf(n);t>-1&&i.splice(t,1)}),2500)}}function ht(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,i=0;i<this.lastTouches.length;i++){var r=this.lastTouches[i],o=Math.abs(e-r.x),s=Math.abs(n-r.y);if(o<=25&&s<=25)return!0}return!1}_(at,F,{handler:function(t,e,n){var i="touch"==n.pointerType,r="mouse"==n.pointerType;if(!(r&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)ct.call(this,e,n);else if(r&&ht.call(this,n))return;this.callback(t,e,n)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var lt=O(h.style,"touchAction"),ft=undefined!==lt,pt=function(){if(!ft)return!1;var t={},e=r.CSS&&r.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){t[n]=!e||r.CSS.supports("touch-action",n)})),t}();function dt(t,e){this.manager=t,this.set(e)}dt.prototype={set:function(t){"compute"==t&&(t=this.compute()),ft&&this.manager.element.style&&pt[t]&&(this.manager.element.style[lt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return v(this.manager.recognizers,(function(e){b(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(B(t,"none"))return"none";var e=B(t,"pan-x"),n=B(t,"pan-y");if(e&&n)return"none";if(e||n)return e?"pan-x":"pan-y";if(B(t,"manipulation"))return"manipulation";return"auto"}(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var i=this.actions,r=B(i,"none")&&!pt.none,o=B(i,"pan-y")&&!pt["pan-y"],s=B(i,"pan-x")&&!pt["pan-x"];if(r){var a=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(a&&c&&u)return}if(!s||!o)return r||o&&6&n||s&&24&n?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};function gt(t){this.options=c({},this.defaults,t||{}),this.id=Q++,this.manager=null,this.options.enable=x(this.options.enable,!0),this.state=1,this.simultaneous={},this.requireFail=[]}function vt(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}function At(t){return 16==t?"down":8==t?"up":2==t?"left":4==t?"right":""}function mt(t,e){var n=e.manager;return n?n.get(t):t}function yt(){gt.apply(this,arguments)}function _t(){yt.apply(this,arguments),this.pX=null,this.pY=null}function wt(){yt.apply(this,arguments)}function bt(){gt.apply(this,arguments),this._timer=null,this._input=null}function xt(){yt.apply(this,arguments)}function kt(){yt.apply(this,arguments)}function Ct(){gt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function It(t,e){return(e=e||{}).recognizers=x(e.recognizers,It.defaults.preset),new Bt(t,e)}gt.prototype={defaults:{},set:function(t){return c(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(g(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=mt(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return g(t,"dropRecognizeWith",this)||(t=mt(t,this),delete this.simultaneous[t.id]),this},requireFailure:function(t){if(g(t,"requireFailure",this))return this;var e=this.requireFail;return-1===S(e,t=mt(t,this))&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(g(t,"dropRequireFailure",this))return this;t=mt(t,this);var e=S(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,n=this.state;function i(n){e.manager.emit(n,t)}n<8&&i(e.options.event+vt(n)),i(e.options.event),t.additionalEvent&&i(t.additionalEvent),n>=8&&i(e.options.event+vt(n))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=32},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},recognize:function(t){var e=c({},t);if(!b(this.options.enable,[this,e]))return this.reset(),void(this.state=32);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},_(yt,gt,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,i=6&e,r=this.attrTest(t);return i&&(8&n||!r)?16|e:i||r?4&n?8|e:2&e?4|e:2:32}}),_(_t,yt,{defaults:{event:"pan",threshold:10,pointers:1,direction:30},getTouchAction:function(){var t=this.options.direction,e=[];return 6&t&&e.push("pan-y"),24&t&&e.push("pan-x"),e},directionTest:function(t){var e=this.options,n=!0,i=t.distance,r=t.direction,o=t.deltaX,s=t.deltaY;return r&e.direction||(6&e.direction?(r=0===o?1:o<0?2:4,n=o!=this.pX,i=Math.abs(t.deltaX)):(r=0===s?1:s<0?8:16,n=s!=this.pY,i=Math.abs(t.deltaY))),t.direction=r,n&&i>e.threshold&&r&e.direction},attrTest:function(t){return yt.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=At(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),_(wt,yt,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return["none"]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||2&this.state)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),_(bt,gt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return["auto"]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime>e.time;if(this._input=t,!i||!n||12&t.eventType&&!r)this.reset();else if(1&t.eventType)this.reset(),this._timer=d((function(){this.state=8,this.tryEmit()}),e.time,this);else if(4&t.eventType)return 8;return 32},reset:function(){clearTimeout(this._timer)},emit:function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=p(),this.manager.emit(this.options.event,this._input)))}}),_(xt,yt,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return["none"]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||2&this.state)}}),_(kt,yt,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},getTouchAction:function(){return _t.prototype.getTouchAction.call(this)},attrTest:function(t){var e,n=this.options.direction;return 30&n?e=t.overallVelocity:6&n?e=t.overallVelocityX:24&n&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&n&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&f(e)>this.options.velocity&&4&t.eventType},emit:function(t){var e=At(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),_(Ct,gt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return["manipulation"]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime<e.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(i&&r&&n){if(4!=t.eventType)return this.failTimeout();var o=!this.pTime||t.timeStamp-this.pTime<e.interval,s=!this.pCenter||z(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,s&&o?this.count+=1:this.count=1,this._input=t,0===this.count%e.taps)return this.hasRequireFailures()?(this._timer=d((function(){this.state=8,this.tryEmit()}),e.interval,this),2):8}return 32},failTimeout:function(){return this._timer=d((function(){this.state=32}),this.options.interval,this),32},reset:function(){clearTimeout(this._timer)},emit:function(){8==this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),It.VERSION="2.0.7",It.defaults={domEvents:!1,touchAction:"compute",enable:!0,inputTarget:null,inputClass:null,preset:[[xt,{enable:!1}],[wt,{enable:!1},["rotate"]],[kt,{direction:6}],[_t,{direction:6},["swipe"]],[Ct],[Ct,{event:"doubletap",taps:2},["tap"]],[bt]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};function Bt(t,e){var n;this.options=c({},It.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(Y?tt:j?ot:G?at:$))(n,L),this.touchAction=new dt(this,this.options.touchAction),Et(this,!0),v(this.options.recognizers,(function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}function Et(t,e){var n,i=t.element;i.style&&(v(t.options.cssProps,(function(r,o){n=O(i.style,o),e?(t.oldCssProps[n]=i.style[n],i.style[n]=r):i.style[n]=t.oldCssProps[n]||""})),e||(t.oldCssProps={}))}Bt.prototype={set:function(t){return c(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?2:1},recognize:function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var i=this.recognizers,r=e.curRecognizer;(!r||r&&8&r.state)&&(r=e.curRecognizer=null);for(var o=0;o<i.length;)n=i[o],2===e.stopped||r&&n!=r&&!n.canRecognizeWith(r)?n.reset():n.recognize(t),!r&&14&n.state&&(r=e.curRecognizer=n),o++}},get:function(t){if(t instanceof gt)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];return null},add:function(t){if(g(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(g(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,n=S(e,t);-1!==n&&(e.splice(n,1),this.touchAction.update())}return this},on:function(t,e){if(undefined!==t&&undefined!==e){var n=this.handlers;return v(E(t),(function(t){n[t]=n[t]||[],n[t].push(e)})),this}},off:function(t,e){if(undefined!==t){var n=this.handlers;return v(E(t),(function(t){e?n[t]&&n[t].splice(S(n[t],e),1):delete n[t]})),this}},emit:function(t,e){this.options.domEvents&&function(t,e){var n=o.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var i=0;i<n.length;)n[i](e),i++}},destroy:function(){this.element&&Et(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},c(It,{INPUT_START:1,INPUT_MOVE:2,INPUT_END:4,INPUT_CANCEL:8,STATE_POSSIBLE:1,STATE_BEGAN:2,STATE_CHANGED:4,STATE_ENDED:8,STATE_RECOGNIZED:8,STATE_CANCELLED:16,STATE_FAILED:32,DIRECTION_NONE:1,DIRECTION_LEFT:2,DIRECTION_RIGHT:4,DIRECTION_UP:8,DIRECTION_DOWN:16,DIRECTION_HORIZONTAL:6,DIRECTION_VERTICAL:24,DIRECTION_ALL:30,Manager:Bt,Input:F,TouchAction:dt,TouchInput:ot,MouseInput:$,PointerEventInput:tt,TouchMouseInput:at,SingleTouchInput:nt,Recognizer:gt,AttrRecognizer:yt,Tap:Ct,Pan:_t,Swipe:kt,Pinch:wt,Rotate:xt,Press:bt,on:k,off:C,each:v,merge:y,extend:m,assign:c,inherit:_,bindFn:w,prefixed:O}),(void 0!==r?r:"undefined"!=typeof self?self:{}).Hammer=It,undefined===(i=function(){return It}.call(e,n,e,t))||(t.exports=i)}(window,document)},function(t,e){function n(e){return t.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},n(e)}t.exports=n},function(t,e){function n(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?t.exports=n=function(t){return typeof t}:t.exports=n=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(e)}t.exports=n},function(t,e){function n(t,e,n,i,r,o,s){try{var a=t[o](s),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(i,r)}t.exports=function(t){return function(){var e=this,i=arguments;return new Promise((function(r,o){var s=t.apply(e,i);function a(t){n(s,r,o,a,c,"next",t)}function c(t){n(s,r,o,a,c,"throw",t)}a(void 0)}))}}},function(t,e,n){var i=n(46);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&i(t,e)}},function(t,e,n){var i=n(10),r=n(28);t.exports=function(t,e){return!e||"object"!==i(e)&&"function"!=typeof e?r(t):e}},function(t,e){t.exports=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},function(t,e,n){var i=n(45);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("21fde573",i,!1,{})},function(t,e,n){var i=n(48);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("68f243ea",i,!1,{})},function(t,e,n){var i=n(50);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("9895d3a6",i,!1,{})},function(t,e,n){var i=n(52);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("5b620605",i,!1,{})},function(t,e,n){var i=n(54);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("1db01c0b",i,!1,{})},function(t,e,n){var i=n(56);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("12d2309d",i,!1,{})},function(t,e,n){var i=n(58);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("1b34bfeb",i,!1,{})},function(t,e,n){var i=n(60);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("8139036a",i,!1,{})},function(t,e,n){var i=n(62);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("604bf5ef",i,!1,{})},function(t,e,n){var i=n(64);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("f32fd36e",i,!1,{})},function(t,e,n){var i=n(66);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("fd83689e",i,!1,{})},function(t,e,n){var i=n(68);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("143dffab",i,!1,{})},function(t,e,n){var i=n(70);"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n(5).default)("550b47ab",i,!1,{})},function(t,e){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},function(t,e,n){var i=n(41),r=n(42),o=n(43);function s(t,e){if(!i.isSortableArrayLike(t))throw new Error("Invalid data");if(!e||t.length>0&&!(e in t[0]))throw new Error("Invalid index");this.data=t,this.index=e,this.setBoundaries(),this.compare="number"==typeof this.minv?r.numcmp:r.strcmp,this.search=o.search,this.valpos={},this.cursor=null,this.nextlow=null,this.nexthigh=null}t.exports=s,s.prototype.setCompare=function(t){if("function"!=typeof t)throw new Error("Invalid argument");return this.compare=t,this},s.prototype.setSearch=function(t){if("function"!=typeof t)throw new Error("Invalid argument");return this.search=t,this},s.prototype.sort=function(){var t=this,e=this.index;return this.data.sort((function(n,i){return t.compare(n[e],i[e])})),this.setBoundaries(),this},s.prototype.setBoundaries=function(){var t=this.data,e=this.index;return this.minv=t.length&&t[0][e],this.maxv=t.length&&t[t.length-1][e],this},s.prototype.fetch=function(t){if(0===this.data.length)return this.cursor=null,this.nextlow=null,this.nexthigh=null,this;if(-1===this.compare(t,this.minv))return this.cursor=null,this.nextlow=null,this.nexthigh=0,this;if(1===this.compare(t,this.maxv))return this.cursor=null,this.nextlow=this.data.length-1,this.nexthigh=null,this;var e=this.valpos[t];if(e)return e.found?(this.cursor=e.index,this.nextlow=null,this.nexthigh=null):(this.cursor=null,this.nextlow=e.prev,this.nexthigh=e.next),this;var n=this.search.call(this,t);return this.cursor=n.index,this.nextlow=n.prev,this.nexthigh=n.next,this},s.prototype.get=function(t){t&&this.fetch(t);var e=this.cursor;return null!==e?this.data[e]:null},s.prototype.getRange=function(t,e){if(1===this.compare(t,e))return[];this.fetch(t);var n=this.cursor||this.nexthigh;this.fetch(e);var i=this.cursor||this.nextlow;return null===n||null===i?[]:this.data.slice(n,i+1)}},function(t,e){t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}},function(t,e,n){var i=n(30);t.exports=function(t,e){if(t){if("string"==typeof t)return i(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}}},function(t,e,n){!function(e,n){"use strict";var i,r,o=function(t){return new o.Instance(t)};o.SUPPORT="wheel",o.ADD_EVENT="addEventListener",o.REMOVE_EVENT="removeEventListener",o.PREFIX="",o.READY=!1,o.Instance=function(t){return o.READY||(o.normalise.browser(),o.READY=!0),this.element=t,this.handlers=[],this},o.Instance.prototype={wheel:function(t,e){return o.event.add(this,o.SUPPORT,t,e),"DOMMouseScroll"===o.SUPPORT&&o.event.add(this,"MozMousePixelScroll",t,e),this},unwheel:function(t,e){return void 0===t&&(t=this.handlers.slice(-1)[0])&&(t=t.original),o.event.remove(this,o.SUPPORT,t,e),"DOMMouseScroll"===o.SUPPORT&&o.event.remove(this,"MozMousePixelScroll",t,e),this}},o.event={add:function(t,n,i,r){var s=i;i=function(t){t||(t=e.event);var n=o.normalise.event(t),i=o.normalise.delta(t);return s(n,i[0],i[1],i[2])},t.element[o.ADD_EVENT](o.PREFIX+n,i,r||!1),t.handlers.push({original:s,normalised:i})},remove:function(t,e,n,i){for(var r,s=n,a={},c=0,u=t.handlers.length;c<u;++c)a[t.handlers[c].original]=t.handlers[c];for(var h in n=(r=a[s]).normalised,t.element[o.REMOVE_EVENT](o.PREFIX+e,n,i||!1),t.handlers)if(t.handlers[h]==r){t.handlers.splice(h,1);break}}},o.normalise={browser:function(){"onwheel"in n||n.documentMode>=9||(o.SUPPORT=void 0!==n.onmousewheel?"mousewheel":"DOMMouseScroll"),e.addEventListener||(o.ADD_EVENT="attachEvent",o.REMOVE_EVENT="detachEvent",o.PREFIX="on")},event:function(t){var e={originalEvent:t,target:t.target||t.srcElement,type:"wheel",deltaMode:"MozMousePixelScroll"===t.type?0:1,deltaX:0,deltaZ:0,preventDefault:function(){t.preventDefault?t.preventDefault():t.returnValue=!1},stopPropagation:function(){t.stopPropagation?t.stopPropagation():t.cancelBubble=!1}};return t.wheelDelta&&(e.deltaY=-1/40*t.wheelDelta),t.wheelDeltaX&&(e.deltaX=-1/40*t.wheelDeltaX),t.detail&&(e.deltaY=t.detail),e},delta:function(t){var e,n,o,s=0,a=0,c=0;return t.deltaY&&(s=c=-1*t.deltaY),t.deltaX&&(s=-1*(a=t.deltaX)),t.wheelDelta&&(s=t.wheelDelta),t.wheelDeltaY&&(c=t.wheelDeltaY),t.wheelDeltaX&&(a=-1*t.wheelDeltaX),t.detail&&(s=-1*t.detail),0===s?[0,0,0]:(e=Math.abs(s),(!i||e<i)&&(i=e),n=Math.max(Math.abs(c),Math.abs(a)),(!r||n<r)&&(r=n),o=s>0?"floor":"ceil",[s=Math[o](s/i),a=Math[o](a/r),c=Math[o](c/r)])}},"function"==typeof e.define&&e.define.amd?e.define("hamster",[],(function(){return o})):t.exports=o}(window,window.document)},function(t){t.exports=JSON.parse('["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**************************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"]')},function(t,e,n){var i,r=function(){var t=String.fromCharCode,e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",i={};function r(t,e){if(!i[t]){i[t]={};for(var n=0;n<t.length;n++)i[t][t.charAt(n)]=n}return i[t][e]}var o={compressToBase64:function(t){if(null==t)return"";var n=o._compress(t,6,(function(t){return e.charAt(t)}));switch(n.length%4){default:case 0:return n;case 1:return n+"===";case 2:return n+"==";case 3:return n+"="}},decompressFromBase64:function(t){return null==t?"":""==t?null:o._decompress(t.length,32,(function(n){return r(e,t.charAt(n))}))},compressToUTF16:function(e){return null==e?"":o._compress(e,15,(function(e){return t(e+32)}))+" "},decompressFromUTF16:function(t){return null==t?"":""==t?null:o._decompress(t.length,16384,(function(e){return t.charCodeAt(e)-32}))},compressToUint8Array:function(t){for(var e=o.compress(t),n=new Uint8Array(2*e.length),i=0,r=e.length;i<r;i++){var s=e.charCodeAt(i);n[2*i]=s>>>8,n[2*i+1]=s%256}return n},decompressFromUint8Array:function(e){if(null==e)return o.decompress(e);for(var n=new Array(e.length/2),i=0,r=n.length;i<r;i++)n[i]=256*e[2*i]+e[2*i+1];var s=[];return n.forEach((function(e){s.push(t(e))})),o.decompress(s.join(""))},compressToEncodedURIComponent:function(t){return null==t?"":o._compress(t,6,(function(t){return n.charAt(t)}))},decompressFromEncodedURIComponent:function(t){return null==t?"":""==t?null:(t=t.replace(/ /g,"+"),o._decompress(t.length,32,(function(e){return r(n,t.charAt(e))})))},compress:function(e){return o._compress(e,16,(function(e){return t(e)}))},_compress:function(t,e,n){if(null==t)return"";var i,r,o,s={},a={},c="",u="",h="",l=2,f=3,p=2,d=[],g=0,v=0;for(o=0;o<t.length;o+=1)if(c=t.charAt(o),Object.prototype.hasOwnProperty.call(s,c)||(s[c]=f++,a[c]=!0),u=h+c,Object.prototype.hasOwnProperty.call(s,u))h=u;else{if(Object.prototype.hasOwnProperty.call(a,h)){if(h.charCodeAt(0)<256){for(i=0;i<p;i++)g<<=1,v==e-1?(v=0,d.push(n(g)),g=0):v++;for(r=h.charCodeAt(0),i=0;i<8;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,i=0;i<p;i++)g=g<<1|r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=h.charCodeAt(0),i=0;i<16;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete a[h]}else for(r=s[h],i=0;i<p;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++),s[u]=f++,h=String(c)}if(""!==h){if(Object.prototype.hasOwnProperty.call(a,h)){if(h.charCodeAt(0)<256){for(i=0;i<p;i++)g<<=1,v==e-1?(v=0,d.push(n(g)),g=0):v++;for(r=h.charCodeAt(0),i=0;i<8;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,i=0;i<p;i++)g=g<<1|r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=h.charCodeAt(0),i=0;i<16;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete a[h]}else for(r=s[h],i=0;i<p;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++)}for(r=2,i=0;i<p;i++)g=g<<1|1&r,v==e-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;for(;;){if(g<<=1,v==e-1){d.push(n(g));break}v++}return d.join("")},decompress:function(t){return null==t?"":""==t?null:o._decompress(t.length,32768,(function(e){return t.charCodeAt(e)}))},_decompress:function(e,n,i){var r,o,s,a,c,u,h,l=[],f=4,p=4,d=3,g="",v=[],A={val:i(0),position:n,index:1};for(r=0;r<3;r+=1)l[r]=r;for(s=0,c=Math.pow(2,2),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;switch(s){case 0:for(s=0,c=Math.pow(2,8),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;h=t(s);break;case 1:for(s=0,c=Math.pow(2,16),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;h=t(s);break;case 2:return""}for(l[3]=h,o=h,v.push(h);;){if(A.index>e)return"";for(s=0,c=Math.pow(2,d),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;switch(h=s){case 0:for(s=0,c=Math.pow(2,8),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;l[p++]=t(s),h=p-1,f--;break;case 1:for(s=0,c=Math.pow(2,16),u=1;u!=c;)a=A.val&A.position,A.position>>=1,0==A.position&&(A.position=n,A.val=i(A.index++)),s|=(a>0?1:0)*u,u<<=1;l[p++]=t(s),h=p-1,f--;break;case 2:return v.join("")}if(0==f&&(f=Math.pow(2,d),d++),l[h])g=l[h];else{if(h!==p)return null;g=o+o.charAt(0)}v.push(g),l[p++]=o+g.charAt(0),o=g,0==--f&&(f=Math.pow(2,d),d++)}}};return o}();void 0===(i=function(){return r}.call(e,n,e,t))||(t.exports=i)},function(t,e,n){var i=n(30);t.exports=function(t){if(Array.isArray(t))return i(t)}},function(t,e){t.exports=function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}},function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}},function(t,e){t.exports=function(t){if(Array.isArray(t))return t}},function(t,e){t.exports=function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var n=[],i=!0,r=!1,o=void 0;try{for(var s,a=t[Symbol.iterator]();!(i=(s=a.next()).done)&&(n.push(s.value),!e||n.length!==e);i=!0);}catch(t){r=!0,o=t}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return n}}},function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}},function(t,e){t.exports.isSortableArrayLike=function(t){return function(t){return!!(t&&"object"==typeof t&&isFinite(t.length)&&t.length>=0&&t.length===Math.floor(t.length)&&t.length<4294967296)}(t)&&function(t){return!(!t||"object"!=typeof t||"function"!=typeof t.sort)}(t)}},function(t,e){t.exports={numcmp:function(t,e){return t-e},strcmp:function(t,e){return t<e?-1:t>e?1:0}}},function(t,e){function n(t,e,i,r,o){var s=i+e>>>1,a=this.compare(t[s][this.index],r);return a?e>=i?o[r]={found:!1,index:null,prev:a<0?i:i-1,next:a<0?i+1:i}:a>0?n.call(this,t,e,s-1,r,o):n.call(this,t,s+1,i,r,o):o[r]={found:!0,index:s,prev:null,next:null}}t.exports.search=function(t){var e=this.data;return n.call(this,e,0,e.length-1,t,this.valpos)}},function(t,e,n){"use strict";var i=n(15);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-ux-wrapper {\n    position: absolute;\n    display: flex;\n}\n.tvjs-ux-wrapper-pin {\n    position: absolute;\n    width: 9px;\n    height: 9px;\n    z-index: 100;\n    background-color: #23a776;\n    border-radius: 10px;\n    margin-left: -6px;\n    margin-top: -6px;\n    pointer-events: none;\n}\n.tvjs-ux-wrapper-head {\n    position: absolute;\n    height: 23px;\n    width: 100%;\n}\n.tvjs-ux-wrapper-close {\n    position: absolute;\n    width: 11px;\n    height: 11px;\n    font-size: 1.5em;\n    line-height: 0.5em;\n    padding: 1px 1px 1px 1px;\n    border-radius: 10px;\n    right: 5px;\n    top: 5px;\n    user-select: none;\n    text-align: center;\n}\n.tvjs-ux-wrapper-close-hb {\n}\n.tvjs-ux-wrapper-close:hover {\n    background-color: #FF605C !important;\n    color: #692324 !important;\n}\n.tvjs-ux-wrapper-full {\n}\n",""]),t.exports=e},function(t,e){function n(e,i){return t.exports=n=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},n(e,i)}t.exports=n},function(t,e,n){"use strict";var i=n(16);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.t-vue-lbtn {\n    z-index: 100;\n    pointer-events: all;\n    cursor: pointer;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(17);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.t-vue-lbtn-grp {\n    margin-left: 0.5em;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(18);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.tvjs-spinner {\n    display: inline-block;\n    position: relative;\n    width: 20px;\n    height: 16px;\n    margin: -4px 0px -1px 0px;\n    opacity: 0.7;\n}\n.tvjs-spinner div {\n    position: absolute;\n    top: 8px;\n    width: 4px;\n    height: 4px;\n    border-radius: 50%;\n    animation-timing-function: cubic-bezier(1, 1, 1, 1);\n}\n.tvjs-spinner div:nth-child(1) {\n    left: 2px;\n    animation: tvjs-spinner1 0.6s infinite;\n    opacity: 0.9;\n}\n.tvjs-spinner div:nth-child(2) {\n    left: 2px;\n    animation: tvjs-spinner2 0.6s infinite;\n}\n.tvjs-spinner div:nth-child(3) {\n    left: 9px;\n    animation: tvjs-spinner2 0.6s infinite;\n}\n.tvjs-spinner div:nth-child(4) {\n    left: 16px;\n    animation: tvjs-spinner3 0.6s infinite;\n    opacity: 0.9;\n}\n@keyframes tvjs-spinner1 {\n0% {\n        transform: scale(0);\n}\n100% {\n        transform: scale(1);\n}\n}\n@keyframes tvjs-spinner3 {\n0% {\n        transform: scale(1);\n}\n100% {\n        transform: scale(0);\n}\n}\n@keyframes tvjs-spinner2 {\n0% {\n        transform: translate(0, 0);\n}\n100% {\n        transform: translate(7px, 0);\n}\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(19);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-legend {\n    position: relative;\n    z-index: 100;\n    font-size: 1.25em;\n    margin-left: 10px;\n    pointer-events: none;\n    text-align: left;\n    user-select: none;\n    font-weight: 300;\n}\n@media (min-resolution: 2x) {\n.trading-vue-legend {\n        font-weight: 400;\n}\n}\n.trading-vue-ohlcv {\n    pointer-events: none;\n    margin-bottom: 0.5em;\n}\n.t-vue-lspan {\n    font-variant-numeric: tabular-nums;\n    font-size: 0.95em;\n    color: #999999; /* TODO: move => params */\n    margin-left: 0.1em;\n    margin-right: 0.2em;\n}\n.t-vue-title {\n    margin-right: 0.25em;\n    font-size: 1.45em;\n}\n.t-vue-ind {\n    margin-left: 0.2em;\n    margin-bottom: 0.5em;\n    font-size: 1.0em;\n    margin-top: 0.3em;\n}\n.t-vue-ivalue {\n    margin-left: 0.5em;\n}\n.t-vue-unknown {\n    color: #999999; /* TODO: move => params */\n}\n.tvjs-appear-enter-active,\n.tvjs-appear-leave-active\n{\n    transition: all .25s ease;\n}\n.tvjs-appear-enter, .tvjs-appear-leave-to\n{\n    opacity: 0;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(20);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-section {\n    height: 0;\n    position: absolute;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(21);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-botbar {\n    position: relative !important;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(22);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.tvjs-item-list {\n    position: absolute;\n    user-select: none;\n    margin-top: -5px;\n}\n.tvjs-item-list-item {\n    display: flex;\n    align-items: center;\n    padding-right: 20px;\n    font-size: 1.15em;\n    letter-spacing: 0.05em;\n}\n.tvjs-item-list-item:hover {\n    background-color: #76878319;\n}\n.tvjs-item-list-item * {\n    position: relative !important;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(23);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-tbitem {\n}\n.trading-vue-tbitem:hover {\n    background-color: #76878319;\n}\n.trading-vue-tbitem-exp {\n    position: absolute;\n    right: -3px;\n    padding: 18.5px 5px;\n    font-stretch: extra-condensed;\n    transform: scaleX(0.6);\n    font-size: 0.6em;\n    opacity: 0.0;\n    user-select: none;\n    line-height: 0;\n}\n.trading-vue-tbitem:hover\n.trading-vue-tbitem-exp {\n    opacity: 0.5;\n}\n.trading-vue-tbitem-exp:hover {\n    background-color: #76878330;\n    opacity: 0.9 !important;\n}\n.trading-vue-tbicon {\n    position: absolute;\n}\n.trading-vue-tbitem.selected-item > .trading-vue-tbicon,\n.tvjs-item-list-item.selected-item > .trading-vue-tbicon {\n     filter: brightness(1.45) sepia(1) hue-rotate(90deg) saturate(4.5) !important;\n}\n.tvjs-pixelated {\n    -ms-interpolation-mode: nearest-neighbor;\n    image-rendering: -webkit-optimize-contrast;\n    image-rendering: -webkit-crisp-edges;\n    image-rendering: -moz-crisp-edges;\n    image-rendering: -o-crisp-edges;\n    image-rendering: pixelated;\n}\n\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(24);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.trading-vue-toolbar {\n    position: absolute;\n    border-right: 1px solid black;\n    z-index: 101;\n    padding-top: 3px;\n    user-select: none;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(25);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.tvjs-widgets {\n    position: absolute;\n    z-index: 1000;\n    pointer-events: none;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(26);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n.tvjs-drift-enter-active {\n    transition: all .3s ease;\n}\n.tvjs-drift-leave-active {\n    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);\n}\n.tvjs-drift-enter, .tvjs-drift-leave-to\n{\n    transform: translateX(10px);\n    opacity: 0;\n}\n.tvjs-the-tip {\n    position: absolute;\n    width: 200px;\n    text-align: center;\n    z-index: 10001;\n    color: #ffffff;\n    font-size: 1.5em;\n    line-height: 1.15em;\n    padding: 10px;\n    border-radius: 3px;\n    right: 70px;\n    top: 10px;\n    text-shadow: 1px 1px black;\n}\n",""]),t.exports=e},function(t,e,n){"use strict";var i=n(27);n.n(i).a},function(t,e,n){(e=n(4)(!1)).push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* Anit-boostrap tactix */\n.trading-vue *, ::after, ::before {\n    box-sizing: content-box;\n}\n.trading-vue img {\n    vertical-align: initial;\n}\n",""]),t.exports=e},function(t,e,n){var i=function(t){"use strict";var e=Object.prototype,n=e.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function a(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{a({},"")}catch(t){a=function(t,e,n){return t[e]=n}}function c(t,e,n,i){var r=e&&e.prototype instanceof l?e:l,o=Object.create(r.prototype),s=new x(i||[]);return o._invoke=function(t,e,n){var i="suspendedStart";return function(r,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw o;return C()}for(n.method=r,n.arg=o;;){var s=n.delegate;if(s){var a=_(s,n);if(a){if(a===h)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===i)throw i="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i="executing";var c=u(t,e,n);if("normal"===c.type){if(i=n.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i="completed",n.method="throw",n.arg=c.arg)}}}(t,n,s),o}function u(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var h={};function l(){}function f(){}function p(){}var d={};d[r]=function(){return this};var g=Object.getPrototypeOf,v=g&&g(g(k([])));v&&v!==e&&n.call(v,r)&&(d=v);var A=p.prototype=l.prototype=Object.create(d);function m(t){["next","throw","return"].forEach((function(e){a(t,e,(function(t){return this._invoke(e,t)}))}))}function y(t,e){var i;this._invoke=function(r,o){function s(){return new e((function(i,s){!function i(r,o,s,a){var c=u(t[r],t,o);if("throw"!==c.type){var h=c.arg,l=h.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){i("next",t,s,a)}),(function(t){i("throw",t,s,a)})):e.resolve(l).then((function(t){h.value=t,s(h)}),(function(t){return i("throw",t,s,a)}))}a(c.arg)}(r,o,i,s)}))}return i=i?i.then(s,s):s()}}function _(t,e){var n=t.iterator[e.method];if(undefined===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method))return h;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var i=u(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var r=i.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,h):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function w(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(w,this),this.reset(!0)}function k(t){if(t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function e(){for(;++i<t.length;)if(n.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:C}}function C(){return{value:undefined,done:!0}}return f.prototype=A.constructor=p,p.constructor=f,f.displayName=a(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,a(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},t.awrap=function(t){return{__await:t}},m(y.prototype),y.prototype[o]=function(){return this},t.AsyncIterator=y,t.async=function(e,n,i,r,o){void 0===o&&(o=Promise);var s=new y(c(e,n,i,r),o);return t.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},m(A),a(A,s,"Generator"),A[r]=function(){return this},A.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var i=e.pop();if(i in t)return n.value=i,n.done=!1,n}return n.done=!0,n}},t.values=k,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=undefined)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(n,i){return s.type="throw",s.arg=t,e.next=n,i&&(e.method="next",e.arg=undefined),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var a=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),b(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;b(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:k(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=undefined),h}},t}(t.exports);try{regeneratorRuntime=i}catch(t){Function("r","regeneratorRuntime = r")(i)}},function(t,e){},function(t,e,n){"use strict";n.r(e),n.d(e,"TradingVue",(function(){return Tn})),n.d(e,"Overlay",(function(){return ft})),n.d(e,"Utils",(function(){return y})),n.d(e,"Constants",(function(){return l})),n.d(e,"Candle",(function(){return Et})),n.d(e,"Volbar",(function(){return St})),n.d(e,"layout_cnv",(function(){return Ct})),n.d(e,"layout_vol",(function(){return It})),n.d(e,"DataCube",(function(){return ni})),n.d(e,"Tool",(function(){return Kt})),n.d(e,"Interface",(function(){return ii})),n.d(e,"primitives",(function(){return ri}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"trading-vue",style:{color:this.chart_props.colors.text,font:this.font_comp,width:this.width+"px",height:this.height+"px"},attrs:{id:t.id},on:{mousedown:t.mousedown,mouseleave:t.mouseleave}},[t.toolbar?n("toolbar",t._b({ref:"toolbar",attrs:{config:t.chart_config},on:{"custom-event":t.custom_event}},"toolbar",t.chart_props,!1)):t._e(),t._v(" "),t.controllers.length?n("widgets",{ref:"widgets",attrs:{map:t.ws,width:t.width,height:t.height,tv:this,dc:t.data}}):t._e(),t._v(" "),n("chart",t._b({key:t.reset,ref:"chart",attrs:{tv_id:t.id,config:t.chart_config},on:{"custom-event":t.custom_event,"range-changed":t.range_changed,"legend-button-click":t.legend_button}},"chart",t.chart_props,!1)),t._v(" "),n("transition",{attrs:{name:"tvjs-drift"}},[t.tip?n("the-tip",{attrs:{data:t.tip},on:{"remove-me":function(e){t.tip=null}}}):t._e()],1)],1)};i._withStripped=!0;var r=n(1),o=n.n(r),s=36e5,a=24*s,c=24192e5,u=365*a,h={SBMIN:60,SBMAX:1/0,TOOLBAR:57,TB_ICON:25,TB_ITEM_M:6,TB_ICON_BRI:1,TB_ICON_HOLD:420,TB_BORDER:1,TB_B_STYLE:"dotted",TOOL_COLL:7,EXPAND:.15,CANDLEW:.6,GRIDX:100,GRIDY:47,BOTBAR:28,PANHEIGHT:22,DEFAULT_LEN:50,MINIMUM_LEN:5,MIN_ZOOM:25,MAX_ZOOM:1e3,VOLSCALE:.15,UX_OPACITY:.9,ZOOM_MODE:"tv",L_BTN_SIZE:21,L_BTN_MARGIN:"-6px 0 -6px 0",SCROLL_WHEEL:"prevent",FONT:"11px -apple-system,BlinkMacSystemFont,\n    Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,\n    Fira Sans,Droid Sans,Helvetica Neue,\n    sans-serif"},l={SECOND:1e3,MINUTE:6e4,MINUTE5:3e5,MINUTE15:9e5,MINUTE30:18e5,HOUR:s,HOUR4:144e5,DAY:a,WEEK:6048e5,MONTH:c,YEAR:u,MONTHMAP:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],TIMESCALES:[10*u,5*u,3*u,2*u,u,6*c,4*c,3*c,2*c,c,15*a,10*a,7*a,5*a,3*a,2*a,a,12*s,6*s,3*s,1.5*s,s,18e5,9e5,6e5,3e5,12e4,6e4],$SCALES:[.05,.1,.2,.25,.5,.8,1,2,5],ChartConfig:h,map_unit:{"1s":1e3,"5s":5e3,"10s":1e4,"20s":2e4,"30s":3e4,"1m":6e4,"3m":18e4,"5m":3e5,"15m":9e5,"30m":18e5,"1H":s,"2H":2*s,"3H":3*s,"4H":144e5,"12H":432e5,"1D":a,"1W":6048e5,"1M":c,"1Y":u},IB_TF_WARN:"When using IB mode you should specify timeframe ('tf' filed in 'chart' object),otherwise you can get an unexpected behaviour"},f=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"trading-vue-chart",style:t.styles},[n("keyboard",{ref:"keyboard"}),t._v(" "),t._l(this._layout.grids,(function(e,i){return n("grid-section",{key:e.id,ref:"sec",refInFor:!0,attrs:{common:t.section_props(i),grid_id:i},on:{"register-kb-listener":t.register_kb,"remove-kb-listener":t.remove_kb,"range-changed":t.range_changed,"cursor-changed":t.cursor_changed,"cursor-locked":t.cursor_locked,"sidebar-transform":t.set_ytransform,"layer-meta-props":t.layer_meta_props,"custom-event":t.emit_custom_event,"legend-button-click":t.legend_button_click}})})),t._v(" "),n("botbar",t._b({attrs:{shaders:t.shaders,timezone:t.timezone}},"botbar",t.botbar_props,!1))],2)};f._withStripped=!0;var p=n(3),d=n.n(p);var g,v=function(t){var e=document.createElement("canvas").getContext("2d");return e.font=t.font,e},A=n(29),m=n.n(A),y={clamp:function(t,e,n){return t<=e?e:t>=n?n:t},add_zero:function(t){return t<10&&(t="0"+t),t},day_start:function(t){return new Date(t).setUTCHours(0,0,0,0)},month_start:function(t){var e=new Date(t);return Date.UTC(e.getFullYear(),e.getMonth(),1)},year_start:function(t){return Date.UTC(new Date(t).getFullYear())},get_year:function(t){if(t)return new Date(t).getUTCFullYear()},get_month:function(t){if(t)return new Date(t).getUTCMonth()},nearest_a:function(t,e){for(var n=1/0,i=null,r=-1,o=0;o<e.length;o++){var s=e[o];Math.abs(s-t)<n&&(n=Math.abs(s-t),i=s,r=o)}return[r,i]},round:function(t,e){return void 0===e&&(e=8),parseFloat(t.toFixed(e))},strip:function(t){return parseFloat(parseFloat(t).toPrecision(12))},get_day:function(t){return t?new Date(t).getDate():null},overwrite:function(t,e){t.splice.apply(t,[0,t.length].concat(o()(e)))},copy_layout:function(t,e){for(var n in t)if(Array.isArray(t[n])){if(t[n].length!==e[n].length){this.overwrite(t[n],e[n]);continue}for(var i in t[n])Object.assign(t[n][i],e[n][i])}else Object.assign(t[n],e[n])},detect_interval:function(t){var e=Math.min(t.length-1,99),n=1/0;return t.slice(0,e).forEach((function(e,i){var r=t[i+1][0]-e[0];r==r&&r<n&&(n=r)})),n>=l.MONTH&&n<=30*l.DAY?31*l.DAY:n},get_num_id:function(t){return parseInt(t.split("_").pop())},fast_filter:function(t,e,n){if(!t.length)return t;try{var i=new m.a(t,"0");return[i.getRange(e,n),i.valpos[e].next]}catch(i){return[t.filter((function(t){return t[0]>=e&&t[0]<=n})),0]}},fast_filter_i:function(t,e,n){if(!t.length)return t;var i=Math.floor(e);i<0&&(i=0);var r=Math.floor(n+1);return[t.slice(i,r),i]},fast_nearest:function(t,e){var n=new m.a(t,"0");return n.fetch(e),[n.nextlow,n.nexthigh]},now:function(){return(new Date).getTime()},pause:function(t){return new Promise((function(e,n){return setTimeout(e,t)}))},smart_wheel:function(t){var e=Math.abs(t);return e>500?(200+Math.log(e))*Math.sign(t):t},get_deltaX:function(t){return t.originalEvent.deltaX/12},get_deltaY:function(t){return t.originalEvent.deltaY/12},apply_opacity:function(t,e){if(7===t.length){var n=Math.floor(255*e);t+=(n=this.clamp(n,0,255)).toString(16)}return t},parse_tf:function(t){return"string"==typeof t?l.map_unit[t]:t},index_shift:function(t,e){if(!e.length)return 0;for(var n,i=e[0][0],r=1;r<e.length;r++)if(e[r][0]!==i){n=e[r][0];break}for(var o=0;o<t.length;o++)if(t[o][0]===n)return o-r;return 0},measureText:function(t,e,n){var i=t.measureTextOrg(e);if(0===i.width){var r=document,o="tvjs-measure-text",s=r.getElementById(o);if(!s){var a=r.getElementById(n);(s=r.createElement("div")).id=o,s.style.position="absolute",s.style.top="-1000px",a.appendChild(s)}return t.font&&(s.style.font=t.font),s.innerText=e.replace(/ /g,"."),{width:s.offsetWidth}}return i},uuid:function(t){return void 0===t&&(t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"),t.replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)}))},uuid2:function(){return this.uuid("xxxxxxxxxxxx")},warn:function(t,e,n){void 0===n&&(n=0),setTimeout((function(){t()&&console.warn(e)}),n)},is_scr_props_upd:function(t,e){var n=e.find((function(e){return e.v.$uuid===t.v.$uuid}));if(!n)return!1;var i=t.p.settings.$props;return!!i&&i.some((function(e){return t.v[e]!==n.v[e]}))},delayed_exec:function(t){if(!t.script||!t.script.execInterval)return!0;var e=this.now(),n=t.script.execInterval;return(!t.settings.$last_exec||e>t.settings.$last_exec+n)&&(t.settings.$last_exec=e,!0)},format_name:function(t){if(t.name){var e=t.name;for(var n in t.settings||{}){var i=t.settings[n],r=new RegExp("\\$".concat(n),"g");e=e.replace(r,i)}return e}},xmode:function(){return this.is_mobile?"explore":"default"},default_prevented:function(t){return t.original?t.original.defaultPrevented:t.defaultPrevented},is_mobile:(g="undefined"!=typeof window?window:{},"onorientationchange"in g&&(!!navigator.maxTouchPoints||!!navigator.msMaxTouchPoints||"ontouchstart"in g||g.DocumentTouch&&document instanceof g.DocumentTouch))},_={point2line:function(t,e,n){var i=this.tri(t,e,n),r=i.area,o=i.base;return Math.abs(this.tri_h(r,o))},point2seg:function(t,e,n){var i=this.tri(t,e,n),r=i.area,o=i.base,s=this.dot_prod(t,e,n)/o,a=Math.max(-s,0),c=Math.max(s-o,0),u=Math.abs(this.tri_h(r,o));return Math.max(u,a,c)},point2ray:function(t,e,n){var i=this.tri(t,e,n),r=i.area,o=i.base,s=this.dot_prod(t,e,n)/o,a=Math.max(-s,0),c=Math.abs(this.tri_h(r,o));return Math.max(c,a)},tri:function(t,e,n){var i=this.area(t,e,n),r=n[0]-e[0],o=n[1]-e[1];return{area:i,base:Math.sqrt(r*r+o*o)}},area:function(t,e,n){return t[0]*(e[1]-n[1])+e[0]*(n[1]-t[1])+n[0]*(t[1]-e[1])},tri_h:function(t,e){return t/e},dot_prod:function(t,e,n){var i=[n[0]-e[0],n[1]-e[1]],r=[t[0]-e[0],t[1]-e[1]];return i[0]*r[0]+i[1]*r[1]},log:function(t){return Math.sign(t)*Math.log(Math.abs(t)+1)},exp:function(t){return Math.sign(t)*(Math.exp(Math.abs(t))-1)},log_mid:function(t,e){var n=this.log(t[0]),i=n-e/2*(n-this.log(t[1]))/e;return this.exp(i)},re_range:function(t,e,n){var i=this.log(t[0]),r=this.log(t[1]),o=this.log(e),s=this.log(n),a=(o-s)*(i-r)/(i-s);return this.exp(o-a)}},w=function(t,e,n,i){return{x:e,w:t.px_step*i.config.CANDLEW,o:Math.floor(_.log(n[1])*t.A+t.B),h:Math.floor(_.log(n[2])*t.A+t.B),l:Math.floor(_.log(n[3])*t.A+t.B),c:Math.floor(_.log(n[4])*t.A+t.B),raw:n}},b=function(t,e){var n=-e/(_.log(t.$_hi)-_.log(t.$_lo)),i=-_.log(t.$_hi)*n,r=.1*-e,o=1.1*e;t.$_hi=_.exp((r-i)/n),t.$_lo=_.exp((o-i)/n)},x=l.TIMESCALES,k=l.$SCALES,C=l.WEEK,I=l.MONTH,B=l.YEAR,E=l.HOUR,S=l.DAY,T=Number.MAX_SAFE_INTEGER;var M=function(t,e,n){void 0===n&&(n=null);var i=e.sub,r=e.interval,o=e.range,s=e.ctx,a=e.$p,c=e.layers_meta,u=e.height,h=e.y_t,l=e.ti_map,f=e.grid,p=e.timezone,g={ti_map:l},v=c[t],A=null,m=f.logScale;if(v&&Object.keys(v).length){var w=Object.values(v).filter((function(t){return t.y_range}));w.length&&(A=w[0].y_range)}function M(){var t=function(){var t=Math.min(g.B,u);if(t<a.config.GRIDY)return 1;var e=t/a.config.GRIDY,n=g.$_hi;if(g.$_lo>0)var i=g.$_hi/g.$_lo;else i=g.$_hi/1;a.config.GRIDY,parseInt(n.toExponential().split("e")[1]);return Math.pow(i,1/e)}(),e=function(){var t=Math.min(u-g.B,u);if(t<a.config.GRIDY)return 1;var e=t/a.config.GRIDY,n=Math.abs(g.$_lo);if(g.$_hi<0&&g.$_lo<0)var i=Math.abs(g.$_lo/g.$_hi);else i=Math.abs(g.$_lo)/1;a.config.GRIDY,parseInt(n.toExponential().split("e")[1]);return Math.pow(i,1/e)}();return Math.max(t,e)}function O(){if(n)g.t_step=n.t_step,g.px_step=n.px_step,g.startx=n.startx,g.xs=n.xs;else{g.t_step=(p=l.ib?6e4:1,d=(o[1]-o[0])*p*(a.config.GRIDX/a.width),v=x,y.nearest_a(d,v)[1]/p),g.xs=[];for(var t=o[1]-o[0],e=g.spacex/t,s=0;s<i.length;s++){var c=i[s],u=i[s-1]||[],h=g.xs[g.xs.length-1]||[0,[]];Q(u,c,Math.floor((c[0]-o[0])*e));var f=g.xs[g.xs.length-1]||[0,[]];h!==f&&(f[1][0]-h[1][0]<.8*g.t_step&&(f[2]<=h[2]?g.xs.pop():g.xs.splice(g.xs.length-2,1)))}r<C&&e>0&&(function(t,e){if(!g.xs.length||!isFinite(e))return;var n=g.xs[0][1][0];for(;;){n-=g.t_step;var i=Math.floor((n-o[0])*e);if(i<0)break;n%r==0&&g.xs.unshift([i,[n],r])}}(0,e),function(t,e){if(!g.xs.length||!isFinite(e))return;var n=g.xs[g.xs.length-1][1][0];for(;;){n+=g.t_step;var i=Math.floor((n-o[0])*e);if(i>g.spacex)break;n%r==0&&g.xs.push([i,[n],r])}}(0,e))}var p,d,v}function Q(t,e,n,i){var o=l.ib?l.i2t(t[0]):t[0],s=l.ib?l.i2t(e[0]):e[0];l.tf<S&&(o+=p*E,s+=p*E);!t[0]&&r!==B||y.get_year(s)===y.get_year(o)?t[0]&&y.get_month(s)!==y.get_month(o)?g.xs.push([n,e,I]):y.day_start(s)===s?g.xs.push([n,e,S]):e[0]%g.t_step==0&&g.xs.push([n,e,r]):g.xs.push([n,e,B])}function D(){var t=Math.pow(10,-g.prec);g.$_step=Math.max(t,function(){var t=g.$_hi-g.$_lo,e=t*(a.config.GRIDY/u),n=parseInt(t.toExponential().split("e")[1]),i=Math.pow(10,n),r=k.map((function(t){return t*i}));return y.strip(y.nearest_a(e,r)[1])}()),g.ys=[];for(var e=g.$_lo-g.$_lo%g.$_step;e<=g.$_hi;e+=g.$_step){var n=Math.floor(e*g.A+g.B);n>u||g.ys.push([n,y.strip(e)])}}function G(){if(g.$_mult=M(),g.ys=[],i.length){for(var t=Math.abs(i[i.length-1][1]||1),e=function(t){var e=u/a.config.GRIDY,n=1/0,i=t,r=0;for(;n>0;)if(n=Math.floor(_.log(i)*g.A+g.B),i*=g.$_mult,r++>3*e)return 0;return i}(t),n=function(t){var e=u/a.config.GRIDY,n=-1/0,i=t,r=0;for(;n<u&&(n=Math.floor(_.log(i)*g.A+g.B),i*=g.$_mult,!(r++>3*e)););return i}(-t),r=-1/0,o=u/a.config.GRIDY,s=1+(g.$_mult-1)/2,c=e;c>0;c/=g.$_mult){c=Y(c,s);var h=Math.floor(_.log(c)*g.A+g.B);if(g.ys.push([h,y.strip(c)]),h>u)break;if(h-r<.7*a.config.GRIDY)break;if(g.ys.length>o+1)break;r=h}r=1/0;for(c=n;c<0;c/=g.$_mult){c=Y(c,s);var l=Math.floor(_.log(c)*g.A+g.B);if(r-l<.7*a.config.GRIDY)break;if(g.ys.push([l,y.strip(c)]),l<0)break;if(g.ys.length>3*o+1)break;r=l}}}function Y(t,e){var n=Math.sign(t);if((t=Math.abs(t))>10){for(var i=10;i<T;i*=10){if(t/(Math.floor(t/i)*i)>e)break}return i/=10,n*Math.floor(t/i)*i}if(t<1){for(var r=10;r>=1;r--){if(t/y.round(t,r)>e)break}return n*y.round(t,r+1)}return n*Math.floor(t)}return function(){if(n){f=-1/0,p=1/0;for(v=0;v<i.length;v++)for(var t=1;t<i[v].length;t++){var e=i[v][t];e>f&&(f=e),e<p&&(p=e)}if(A)var r=A(f,p),o=d()(r,3),s=(f=o[0],p=o[1],o[2])}else if(A)var c=A(f,p),l=d()(c,2),f=l[0],p=l[1];else{f=-1/0,p=1/0;for(var v=0,y=i.length;v<y;v++){var _=i[v];_[2]>f&&(f=_[2]),_[3]<p&&(p=_[3])}}h&&!h.auto&&h.range?(g.$_hi=h.range[0],g.$_lo=h.range[1]):(m?(g.$_hi=f,g.$_lo=p,b(g,u)):(s=!1===s?0:1,g.$_hi=f+(f-p)*a.config.EXPAND*s,g.$_lo=p-(f-p)*a.config.EXPAND*s),g.$_hi===g.$_lo&&(m?b(g,u):(g.$_hi*=1.05,g.$_lo*=.95)))}(),function(){if(i.length<2)return g.prec=0,void(g.sb=a.config.SBMIN);g.prec=function(t){for(var e=0,n=0,i=1/0,r=-1/0,o=0,s=t.length;o<s;o++){var a=t[o];a[1]>r?r=a[1]:a[1]<i&&(i=a[1])}[i,r].forEach((function(t){var i=null!=t?t.toString():"";if(t<1e-6){var r=i.split("e-"),o=d()(r,2),s=o[0],a=o[1],c=s.split("."),u=d()(c,2),h=u[0];(p=u[1])||(p=""),p={length:p.length+parseInt(a)||0}}else var l=i.split("."),f=d()(l,2),p=(h=f[0],f[1]);p&&p.length>e&&(e=p.length),h&&h.length>n&&(n=h.length)}));var c=e-e%2+2;if(1===n)return Math.min(8,Math.max(2,c));if(n<=2)return Math.min(4,Math.max(2,c));return 2}(i);var t=[];t.push(g.$_hi.toFixed(g.prec).length),t.push(g.$_lo.toFixed(g.prec).length);var e="0".repeat(Math.max.apply(Math,t))+"    ";g.sb=s.measureText(e).width,g.sb=Math.max(Math.floor(g.sb),a.config.SBMIN),g.sb=Math.min(g.sb,a.config.SBMAX)}(),{create:function(){return function(){if(!(i.length<2)){var t=o[1]-o[0];g.spacex=a.width-g.sb;var e=t/r;g.px_step=g.spacex/e;var n=g.spacex/t;g.startx=(i[0][0]-o[0])*n,f.logScale?(g.A=-u/(_.log(g.$_hi)-_.log(g.$_lo)),g.B=-_.log(g.$_hi)*g.A):(g.A=-u/(g.$_hi-g.$_lo),g.B=-g.$_hi*g.A)}}(),O(),f.logScale?G():D(),g.width=a.width-g.sb,g.height=u,n&&(g.master_grid=n),g.grid=f,function(t,e){var n=t.ti_map.ib,i=e[1]-e[0],r=t.spacex/i,o=t.grid.logScale||!1;return Object.assign(t,{t2screen:function(i){return n&&(i=t.ti_map.smth2i(i)),Math.floor((i-e[0])*r)-.5},$2screen:function(e){return o&&(e=_.log(e)),Math.floor(e*t.A+t.B)-.5},t_magnet:function(e){n&&(e=t.ti_map.smth2i(e));var i=t.candles||t.master_grid.candles,r=i.map((function(t){return t.raw[0]})),o=y.nearest_a(e,r)[0];if(i[o])return Math.floor(i[o].x)-.5},screen2$:function(e){return o?_.exp((e-t.B)/t.A):(e-t.B)/t.A},screen2t:function(t){return e[0]+t/r},$_magnet:function(t){},c_magnet:function(e){var n=t.candles||t.master_grid.candles,i=n.map((function(t){return t.raw[0]}));return n[y.nearest_a(e,i)[0]]},data_magnet:function(t){}}),t}(g,o)},get_layout:function(){return g},set_sidebar:function(t){return g.sb=t},get_sidebar:function(){return g.sb}}};function O(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Q(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var D=function(t){var e=t.chart,n=t.sub,i=t.offsub,r=t.interval,s=t.range,a=t.ctx,c=t.layers_meta,u=t.ti_map,h=t.$props,l=t.y_transforms,f=e.grid||{};i=i.filter((function(t,e){return!(t.grid&&t.grid.id)}));var p,g=function(){var t=h.height-h.config.BOTBAR;if(f.height||i.find((function(t){return t.grid.height})))return function(t,e){var n=[{grid:t}].concat(o()(i)).map((function(t){return t.grid.height||1})),r=n.reduce((function(t,e){return t+e}),0);n=n.map((function(t){return Math.floor(t/r*e)})),r=n.reduce((function(t,e){return t+e}),0);for(var s=0;s<e-r;s++)n[s%n.length]++;return n}(f,t);var e=i.length,n=2*Math.sqrt(e)/7/(e||1),r=Math.floor(t*n);return[t-r*e].concat(Array(e).fill(r))}(),v={sub:n,interval:r,range:s,ctx:a,$p:h,layers_meta:c,ti_map:u,height:g[0],y_t:l[0],grid:f,timezone:h.timezone},A=[new M(0,v)],m=O(i.entries());try{for(m.s();!(p=m.n()).done;){var y=d()(p.value,2),_=y[0],b=y[1],x=b.data,k=b.grid;v.sub=x,v.height=g[_+1],v.y_t=l[_+1],v.grid=k||{},A.push(new M(_+1,v,A[0].get_layout()))}}catch(t){m.e(t)}finally{m.f()}var C=Math.max.apply(Math,o()(A.map((function(t){return t.get_sidebar()})))),I=[],B=0;for(_=0;_<A.length;_++)A[_].set_sidebar(C),I.push(A[_].create()),I[_].id=_,I[_].offset=B,B+=I[_].height;var E=I[0];return function(){E.candles=[],E.volume=[];for(var t,e,i,s=Math.max.apply(Math,o()(n.map((function(t){return t[5]})))),a=h.config.VOLSCALE*h.height/s,c=void 0,u=E.px_step>5?1:0,l=.5*E.px_step,p=0;p<n.length;p++){var d=n[p];i=E.t2screen(d[0])+.5,E.candles.push(f.logScale?w(E,i,d,h):{x:i,w:E.px_step*h.config.CANDLEW,o:Math.floor(d[1]*E.A+E.B),h:Math.floor(d[2]*E.A+E.B),l:Math.floor(d[3]*E.A+E.B),c:Math.floor(d[4]*E.A+E.B),raw:d}),n[p-1]&&d[0]-n[p-1][0]>r&&(c=null),t=c||Math.floor(i-l),e=Math.floor(i+l)-.5,E.volume.push({x1:t,x2:e,h:d[5]*a,green:d[4]>=d[1],raw:d}),c=e+u}}(),{grids:I,botbar:{width:h.width,height:h.config.BOTBAR,offset:B,xs:I[0]?I[0].xs:[]}}},G=n(0),Y=n.n(G),j=n(2),U=n.n(j);function R(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return F(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return F(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var L=function(){function t(e){Y()(this,t),this.comp=e,this.grids=e._layout.grids,this.cursor=e.cursor}return U()(t,[{key:"sync",value:function(t){this.cursor.grid_id=t.grid_id;var e,n=!0,i=R(this.grids);try{for(i.s();!(e=i.n()).done;){var r=e.value,o=this.cursor_data(r,t);this.cursor.locked||(n&&(this.cursor.t=this.cursor_time(r,t,o),this.cursor.t&&(n=!1)),o.values&&this.comp.$set(this.cursor.values,r.id,o.values)),r.id===t.grid_id&&(this.cursor.x=r.t2screen(this.cursor.t),this.cursor.y=o.y,this.cursor.y$=o.y$)}}catch(t){i.e(t)}finally{i.f()}}},{key:"overlay_data",value:function(t,e){var n=0===t.id?"main_section":"sub_section",i=this.comp[n].data;if(t.id>0){var r=i.filter((function(t){return void 0===t.grid.id})),s=i.filter((function(e){return e.grid.id===t.id}));i=[r[t.id-1]].concat(o()(s))}var a,c=t.screen2t(e.x),u={},h={},l=R(i);try{for(l.s();!(a=l.n()).done;){var f=a.value,p=f.data.map((function(t){return t[0]})),d=y.nearest_a(c,p)[0];f.type in u?u[f.type]++:u[f.type]=0,h["".concat(f.type,"_").concat(u[f.type])]=f.data[d]}}catch(t){l.e(t)}finally{l.f()}return h}},{key:"cursor_data",value:function(t,e){var n=this.comp.main_section.sub,i=n.map((function(e){return t.t2screen(e[0])+.5})),r=y.nearest_a(e.x,i)[0];return i[r]?{x:Math.floor(i[r])-.5,y:Math.floor(e.y-2)-.5-t.offset,y$:t.screen2$(e.y-2-t.offset),t:(n[r]||[])[0],values:Object.assign({ohlcv:0===t.id?n[r]:void 0},this.overlay_data(t,e))}:{}}},{key:"cursor_time",value:function(t,e,n){var i=t.screen2t(e.x),r=Math.abs((i-n.t)/this.comp.interval),o=Math.sign(i-n.t);if(r>=.5){var s=Math.round(r);return n.t+s*this.comp.interval*o}return n.t}}]),t}(),N=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"trading-vue-section"},[n("chart-legend",{ref:"legend",attrs:{values:t.section_values,grid_id:t.grid_id,common:t.legend_props,meta_props:t.get_meta_props},on:{"legend-button-click":t.button_click}}),t._v(" "),n("grid",t._b({ref:"grid",attrs:{grid_id:t.grid_id},on:{"register-kb-listener":t.register_kb,"remove-kb-listener":t.remove_kb,"range-changed":t.range_changed,"cursor-changed":t.cursor_changed,"cursor-locked":t.cursor_locked,"layer-meta-props":t.emit_meta_props,"custom-event":t.emit_custom_event,"sidebar-transform":t.sidebar_transform,"rezoom-range":t.rezoom_range}},"grid",t.grid_props,!1)),t._v(" "),n("sidebar",t._b({ref:"sb-"+t.grid_id,attrs:{grid_id:t.grid_id,rerender:t.rerender},on:{"sidebar-transform":t.sidebar_transform}},"sidebar",t.sidebar_props,!1))],1)};N._withStripped=!0;var P=function(){function t(e){var n=this;Y()(this,t),this.t0=this.t=y.now(),this.id=setInterval((function(){y.now()-n.t>100||(y.now()-n.t0>1200&&n.stop(),n.id&&e(n),n.t=y.now())}),16)}return U()(t,[{key:"stop",value:function(){clearInterval(this.id),this.id=null}}]),t}(),K=n(8),H=n(32),z=n.n(H);function W(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return J(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return J(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var $=function(){function t(e,n){Y()(this,t),this.MIN_ZOOM=n.config.MIN_ZOOM,this.MAX_ZOOM=n.config.MAX_ZOOM,y.is_mobile&&(this.MIN_ZOOM*=.5),this.canvas=e,this.ctx=e.getContext("2d"),this.comp=n,this.$p=n.$props,this.data=this.$p.sub,this.range=this.$p.range,this.id=this.$p.grid_id,this.layout=this.$p.layout.grids[this.id],this.interval=this.$p.interval,this.cursor=n.$props.cursor,this.offset_x=0,this.offset_y=0,this.deltas=0,this.wmode=this.$p.config.SCROLL_WHEEL,this.listeners(),this.overlays=[]}return U()(t,[{key:"listeners",value:function(){var t=this;this.hm=z()(this.canvas),this.hm.wheel((function(e,n){return t.mousezoom(50*-n,e)}));var e=this.mc=new K.Manager(this.canvas),n=y.is_mobile?10:0;e.add(new K.Pan({threshold:n})),e.add(new K.Tap),e.add(new K.Pinch({threshold:0})),e.get("pinch").set({enable:!0}),y.is_mobile&&e.add(new K.Press),e.on("panstart",(function(e){if(!t.cursor.scroll_lock){if("aim"===t.cursor.mode)return t.emit_cursor_coord(e);var n=t.$p.y_transform;t.drug={x:e.center.x+t.offset_x,y:e.center.y+t.offset_y,r:t.range.slice(),t:t.range[1]-t.range[0],o:n&&n.offset||0,y_r:n&&n.range?n.range.slice():void 0,B:t.layout.B,t0:y.now()},t.comp.$emit("cursor-changed",{grid_id:t.id,x:e.center.x+t.offset_x,y:e.center.y+t.offset_y}),t.comp.$emit("cursor-locked",!0)}})),e.on("panmove",(function(e){y.is_mobile&&(t.calc_offset(),t.propagate("mousemove",t.touch2mouse(e))),t.drug?(t.mousedrag(t.drug.x+e.deltaX,t.drug.y+e.deltaY),t.comp.$emit("cursor-changed",{grid_id:t.id,x:e.center.x+t.offset_x,y:e.center.y+t.offset_y})):"aim"===t.cursor.mode&&t.emit_cursor_coord(e)})),e.on("panend",(function(e){y.is_mobile&&t.drug&&t.pan_fade(e),t.drug=null,t.comp.$emit("cursor-locked",!1)})),e.on("tap",(function(e){y.is_mobile&&(t.sim_mousedown(e),t.fade&&t.fade.stop(),t.comp.$emit("cursor-changed",{}),t.comp.$emit("cursor-changed",{mode:"explore"}),t.update())})),e.on("pinchstart",(function(){t.drug=null,t.pinch={t:t.range[1]-t.range[0],r:t.range.slice()}})),e.on("pinchend",(function(){t.pinch=null})),e.on("pinch",(function(e){t.pinch&&t.pinchzoom(e.scale)})),e.on("press",(function(e){y.is_mobile&&(t.fade&&t.fade.stop(),t.calc_offset(),t.emit_cursor_coord(e,{mode:"aim"}),setTimeout((function(){return t.update()})),t.sim_mousedown(e))}));var i=addEventListener;i("gesturestart",this.gesturestart),i("gesturechange",this.gesturechange),i("gestureend",this.gestureend)}},{key:"gesturestart",value:function(t){t.preventDefault()}},{key:"gesturechange",value:function(t){t.preventDefault()}},{key:"gestureend",value:function(t){t.preventDefault()}},{key:"mousemove",value:function(t){y.is_mobile||(this.comp.$emit("cursor-changed",{grid_id:this.id,x:t.layerX,y:t.layerY+this.layout.offset}),this.calc_offset(),this.propagate("mousemove",t))}},{key:"mouseout",value:function(t){y.is_mobile||(this.comp.$emit("cursor-changed",{}),this.propagate("mouseout",t))}},{key:"mouseup",value:function(t){this.drug=null,this.comp.$emit("cursor-locked",!1),this.propagate("mouseup",t)}},{key:"mousedown",value:function(t){y.is_mobile||(this.propagate("mousedown",t),this.comp.$emit("cursor-locked",!0),t.defaultPrevented||this.comp.$emit("custom-event",{event:"grid-mousedown",args:[this.id,t]}))}},{key:"sim_mousedown",value:function(t){var e=this;t.srcEvent.defaultPrevented||(this.comp.$emit("custom-event",{event:"grid-mousedown",args:[this.id,t]}),this.propagate("mousemove",this.touch2mouse(t)),this.update(),this.propagate("mousedown",this.touch2mouse(t)),setTimeout((function(){e.propagate("click",e.touch2mouse(t))})))}},{key:"touch2mouse",value:function(t){return this.calc_offset(),{original:t.srcEvent,layerX:t.center.x+this.offset_x,layerY:t.center.y+this.offset_y,preventDefault:function(){this.original.preventDefault()}}}},{key:"click",value:function(t){this.propagate("click",t)}},{key:"emit_cursor_coord",value:function(t,e){void 0===e&&(e={}),this.comp.$emit("cursor-changed",Object.assign({grid_id:this.id,x:t.center.x+this.offset_x,y:t.center.y+this.offset_y+this.layout.offset},e))}},{key:"pan_fade",value:function(t){var e=this,n=y.now()-this.drug.t0,i=42*(this.range[1]-this.drug.r[1])/n,r=Math.abs(.01*i);n>500||(this.fade&&this.fade.stop(),this.fade=new P((function(t){i*=.85,Math.abs(i)<r&&t.stop(),e.range[0]+=i,e.range[1]+=i,e.change_range()})))}},{key:"calc_offset",value:function(){var t=this.canvas.getBoundingClientRect();this.offset_x=-t.x,this.offset_y=-t.y}},{key:"new_layer",value:function(t){"crosshair"===t.name?this.crosshair=t:this.overlays.push(t),this.update()}},{key:"del_layer",value:function(t){this.overlays=this.overlays.filter((function(e){return e.id!==t})),this.update()}},{key:"show_hide_layer",value:function(t){var e=this.overlays.filter((function(e){return e.id===t.id}));e.length&&(e[0].display=t.display)}},{key:"update",value:function(){var t=this;if(this.layout=this.$p.layout.grids[this.id],this.interval=this.$p.interval,this.layout){this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.$p.shaders.length&&this.apply_shaders(),this.grid();var e=[];e.push.apply(e,o()(this.overlays)),e.sort((function(t,e){return t.z-e.z})),e.forEach((function(e){if(e.display){t.ctx.save();var n=e.renderer;n.pre_draw&&n.pre_draw(t.ctx),n.draw(t.ctx),n.post_draw&&n.post_draw(t.ctx),t.ctx.restore()}})),this.crosshair&&this.crosshair.renderer.draw(this.ctx)}}},{key:"apply_shaders",value:function(){var t,e=this.$p.layout.grids[this.id],n={layout:e,range:this.range,interval:this.interval,tf:e.ti_map.tf,cursor:this.cursor,colors:this.$p.colors,sub:this.data,font:this.$p.font,config:this.$p.config,meta:this.$p.meta},i=W(this.$p.shaders);try{for(i.s();!(t=i.n()).done;){var r=t.value;this.ctx.save(),r.draw(this.ctx,n),this.ctx.restore()}}catch(t){i.e(t)}finally{i.f()}}},{key:"grid",value:function(){this.ctx.strokeStyle=this.$p.colors.grid,this.ctx.beginPath();var t,e=this.layout.height,n=W(this.layout.xs);try{for(n.s();!(t=n.n()).done;){var i=d()(t.value,2),r=i[0];i[1];this.ctx.moveTo(r-.5,0),this.ctx.lineTo(r-.5,e)}}catch(t){n.e(t)}finally{n.f()}var o,s=W(this.layout.ys);try{for(s.s();!(o=s.n()).done;){var a=d()(o.value,2),c=a[0];a[1];this.ctx.moveTo(0,c-.5),this.ctx.lineTo(this.layout.width,c-.5)}}catch(t){s.e(t)}finally{s.f()}this.ctx.stroke(),this.$p.grid_id&&this.upper_border()}},{key:"upper_border",value:function(){this.ctx.strokeStyle=this.$p.colors.scale,this.ctx.beginPath(),this.ctx.moveTo(0,.5),this.ctx.lineTo(this.layout.width,.5),this.ctx.stroke()}},{key:"mousezoom",value:function(t,e){if("pass"!==this.wmode){if("click"===this.wmode&&!this.$p.meta.activated)return;e.originalEvent.preventDefault(),e.preventDefault()}if(e.deltaX=e.deltaX||y.get_deltaX(e),e.deltaY=e.deltaY||y.get_deltaY(e),Math.abs(e.deltaX)>0&&(this.trackpad=!0,Math.abs(e.deltaX)>=Math.abs(e.deltaY)&&(t*=.1),this.trackpad_scroll(e)),this.trackpad&&(t*=.032),!((t=y.smart_wheel(t))<0&&this.data.length<=this.MIN_ZOOM||t>0&&this.data.length>this.MAX_ZOOM)){var n=t*(this.interval/1e3)*this.data.length,i="tl"===this.comp.config.ZOOM_MODE;if(e.originalEvent.ctrlKey||i){var r=e.originalEvent.offsetX/(this.canvas.width-1)*n,o=n-r;this.range[0]-=r,this.range[1]+=o}else this.range[0]-=n;if(i){var s=e.originalEvent.offsetY/(this.canvas.height-1)*2,a=2-s,c=n/(this.range[1]-this.range[0]);this.comp.$emit("rezoom-range",{grid_id:this.id,z:c,diff1:s,diff2:a})}this.change_range()}}},{key:"mousedrag",value:function(t,e){var n=this.drug.t*(this.drug.x-t)/this.layout.width,i=this.layout.$_hi-this.layout.$_lo;i*=(this.drug.y-e)/this.layout.height;var r=this.drug.o+i,o=this.layout.grid.logScale;if(o&&this.drug.y_r){var s=this.drug.y-e,a=this.drug.y_r.slice();a[0]=_.exp((0-this.drug.B+s)/this.layout.A),a[1]=_.exp((this.layout.height-this.drug.B+s)/this.layout.A)}this.drug.y_r&&this.$p.y_transform&&!this.$p.y_transform.auto&&this.comp.$emit("sidebar-transform",{grid_id:this.id,range:o?a||this.drug.y_r:[this.drug.y_r[0]-r,this.drug.y_r[1]-r]}),this.range[0]=this.drug.r[0]+n,this.range[1]=this.drug.r[1]+n,this.change_range()}},{key:"pinchzoom",value:function(t){if(!(t>1&&this.data.length<=this.MIN_ZOOM||t<1&&this.data.length>this.MAX_ZOOM)){var e=this.pinch.t,n=1*e/t;this.range[0]=this.pinch.r[0]-.5*(n-e),this.range[1]=this.pinch.r[1]+.5*(n-e),this.change_range()}}},{key:"trackpad_scroll",value:function(t){var e=this.range[1]-this.range[0];this.range[0]+=t.deltaX*e*.011,this.range[1]+=t.deltaX*e*.011,this.change_range()}},{key:"change_range",value:function(){if(this.range.length&&!(this.data.length<2)){var t=this.data.length-1,e=this.data,n=this.range;n[0]=y.clamp(n[0],-1/0,e[t][0]-5.5*this.interval),n[1]=y.clamp(n[1],e[0][0]+5.5*this.interval,1/0),this.comp.$emit("range-changed",n)}}},{key:"propagate",value:function(t,e){var n,i=W(this.overlays);try{for(i.s();!(n=i.n()).done;){var r=n.value;r.renderer[t]&&r.renderer[t](e);var o=r.renderer.mouse,s=r.renderer.keys;o.listeners&&o.emit(t,e),s&&s.listeners&&s.emit(t,e)}}catch(t){i.e(t)}finally{i.f()}}},{key:"destroy",value:function(){var t=removeEventListener;t("gesturestart",this.gesturestart),t("gesturechange",this.gesturechange),t("gestureend",this.gestureend),this.mc&&this.mc.destroy(),this.hm&&this.hm.unwheel()}}]),t}(),V={methods:{setup:function(){var t=this,e="".concat(this.$props.tv_id,"-").concat(this._id,"-canvas"),n=document.getElementById(e),i=window.devicePixelRatio||1;n.style.width="".concat(this._attrs.width,"px"),n.style.height="".concat(this._attrs.height,"px"),i<1&&(i=1),this.$nextTick((function(){var e=n.getBoundingClientRect();n.width=e.width*i,n.height=e.height*i;var r=n.getContext("2d",{});r.scale(i,i),t.redraw(),r.measureTextOrg||(r.measureTextOrg=r.measureText),r.measureText=function(e){return y.measureText(r,e,t.$props.tv_id)}}))},create_canvas:function(t,e,n){var i=this;return this._id=e,this._attrs=n.attrs,t("div",{class:"trading-vue-".concat(e),style:{left:n.position.x+"px",top:n.position.y+"px",position:"absolute"}},[t("canvas",{on:{mousemove:function(t){return i.renderer.mousemove(t)},mouseout:function(t){return i.renderer.mouseout(t)},mouseup:function(t){return i.renderer.mouseup(t)},mousedown:function(t){return i.renderer.mousedown(t)}},attrs:Object.assign({id:"".concat(this.$props.tv_id,"-").concat(e,"-canvas")},n.attrs),ref:"canvas",style:n.style})].concat(n.hs||[]))},redraw:function(){this.renderer&&this.renderer.update()}},watch:{width:function(t){this._attrs.width=t,this.setup()},height:function(t){this._attrs.height=t,this.setup()}}},X=function(){function t(e){Y()(this,t),this.comp=e,this.$p=e.$props,this.data=this.$p.sub,this._visible=!1,this.locked=!1,this.layout=this.$p.layout}return U()(t,[{key:"draw",value:function(t){this.layout=this.$p.layout;var e=this.comp.$props.cursor;(this.visible||"explore"!==e.mode)&&(this.x=this.$p.cursor.x,this.y=this.$p.cursor.y,t.save(),t.strokeStyle=this.$p.colors.cross,t.beginPath(),t.setLineDash([5]),this.$p.cursor.grid_id===this.layout.id&&(t.moveTo(0,this.y),t.lineTo(this.layout.width-.5,this.y)),t.moveTo(this.x,0),t.lineTo(this.x,this.layout.height),t.stroke(),t.restore())}},{key:"hide",value:function(){this.visible=!1,this.x=void 0,this.y=void 0}},{key:"visible",get:function(){return this._visible},set:function(t){this._visible=t}}]),t}();function Z(t,e,n,i,r,o,s,a){var c,u="function"==typeof t?t.options:t;if(e&&(u.render=e,u.staticRenderFns=n,u._compiled=!0),i&&(u.functional=!0),o&&(u._scopeId="data-v-"+o),s?(c=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),r&&r.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},u._ssrRegister=c):r&&(c=a?function(){r.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:r),c)if(u.functional){u._injectStyles=c;var h=u.render;u.render=function(t,e){return c.call(e),h(t,e)}}else{var l=u.beforeCreate;u.beforeCreate=l?[].concat(l,c):[c]}return{exports:t,options:u}}var q=Z({name:"Crosshair",props:["cursor","colors","layout","sub"],methods:{create:function(){this.ch=new X(this),this.$emit("new-grid-layer",{name:"crosshair",renderer:this.ch})}},watch:{cursor:{handler:function(){this.ch||this.create();var t=this.$props.cursor,e="explore"===t.mode;if(!t.x||!t.y)return this.ch.hide(),void this.$emit("redraw-grid");this.ch.visible=!e},deep:!0}},render:function(t){return t()}},undefined,undefined,!1,null,null,null);q.options.__file="src/components/Crosshair.vue";var tt=q.exports,et=Z({name:"KeyboardListener",render:function(t){return t()},created:function(){this.$emit("register-kb-listener",{id:this._uid,keydown:this.keydown,keyup:this.keyup,keypress:this.keypress})},beforeDestroy:function(){this.$emit("remove-kb-listener",{id:this._uid})},methods:{keydown:function(t){this.$emit("keydown",t)},keyup:function(t){this.$emit("keyup",t)},keypress:function(t){this.$emit("keypress",t)}}},undefined,undefined,!1,null,null,null);et.options.__file="src/components/KeyboardListener.vue";var nt=et.exports,it=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",{class:"trading-vue-grid-ux-"+t.id,style:t.style},t._l(t.uxs,(function(e){return n("ux-wrapper",{key:e.uuid,attrs:{ux:e,updater:t.updater,colors:t.colors,config:t.config},on:{"custom-event":t.on_custom_event}})})),1)};it._withStripped=!0;var rt=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.visible?n("div",{staticClass:"trading-vue-ux-wrapper",style:t.style,attrs:{id:"tvjs-ux-wrapper-"+t.ux.uuid}},[n(t.ux.component,{tag:"component",attrs:{ux:t.ux,updater:t.updater,wrapper:t.wrapper,colors:t.colors},on:{"custom-event":t.on_custom_event}}),t._v(" "),t.ux.show_pin?n("div",{staticClass:"tvjs-ux-wrapper-pin",style:t.pin_style}):t._e(),t._v(" "),!1!==t.ux.win_header?n("div",{staticClass:"tvjs-ux-wrapper-head"},[n("div",{staticClass:"tvjs-ux-wrapper-close",style:t.btn_style,on:{click:t.close}},[t._v("×")])]):t._e()],1):t._e()};rt._withStripped=!0;var ot={name:"UxWrapper",props:["ux","updater","colors","config"],mounted:function(){this.self=document.getElementById(this.uuid),this.w=this.self.offsetWidth,this.h=this.self.offsetHeight,this.update_position()},created:function(){this.mouse.on("mousemove",this.mousemove),this.mouse.on("mouseout",this.mouseout)},beforeDestroy:function(){this.mouse.off("mousemove",this.mousemove),this.mouse.off("mouseout",this.mouseout)},methods:{update_position:function(){if(!this.uxr.hidden){var t=this.layout.width,e=this.layout.height,n=this.uxr.pin;switch(n[0]){case"cursor":var i=this.uxr.overlay.cursor.x;break;case"mouse":i=this.mouse.x;break;default:i="string"==typeof n[0]?this.parse_coord(n[0],t):this.layout.t2screen(n[0])}switch(n[1]){case"cursor":var r=this.uxr.overlay.cursor.y;break;case"mouse":r=this.mouse.y;break;default:r="string"==typeof n[1]?this.parse_coord(n[1],e):this.layout.$2screen(n[1])}this.x=i+this.ox,this.y=r+this.oy}},parse_coord:function(t,e){if("0"===(t=t.trim())||""===t)return 0;var n=t.split("+");if(2===n.length)return this.parse_coord(n[0],e)+this.parse_coord(n[1],e);var i=t.split("-");if(2===i.length)return this.parse_coord(i[0],e)-this.parse_coord(i[1],e);var r=t.split("%");if(2===r.length)return e*parseInt(r[0])/100;var o=t.split("px");return 2===o.length?parseInt(o[0]):void 0},mousemove:function(){this.update_position(),this.visible=!0},mouseout:function(){(this.uxr.pin.includes("cursor")||this.uxr.pin.includes("mouse"))&&(this.visible=!1)},on_custom_event:function(t){this.$emit("custom-event",t),"modify-interface"===t.event&&(this.self&&(this.w=this.self.offsetWidth,this.h=this.self.offsetHeight),this.update_position())},close:function(){this.$emit("custom-event",{event:"close-interface",args:[this.$props.ux.uuid]})}},computed:{uxr:function(){return this.$props.ux},layout:function(){return this.$props.ux.overlay.layout},settings:function(){return this.$props.ux.overlay.settings},uuid:function(){return"tvjs-ux-wrapper-".concat(this.uxr.uuid)},mouse:function(){return this.uxr.overlay.mouse},style:function(){var t={display:this.uxr.hidden?"none":void 0,left:"".concat(this.x,"px"),top:"".concat(this.y,"px"),"pointer-events":this.uxr.pointer_events||"all","z-index":this.z_index};return!1!==this.uxr.win_styling&&(t=Object.assign(t,{border:"1px solid ".concat(this.$props.colors.grid),"border-radius":"3px",background:"".concat(this.background)})),t},pin_style:function(){return{left:"".concat(-this.ox,"px"),top:"".concat(-this.oy,"px"),"background-color":this.uxr.pin_color}},btn_style:function(){return{background:"".concat(this.inactive_btn_color),color:"".concat(this.inactive_btn_color)}},pin_pos:function(){return this.uxr.pin_position?this.uxr.pin_position.split(","):["0","0"]},ox:function(){if(2===this.pin_pos.length)return-this.parse_coord(this.pin_pos[0],this.w)},oy:function(){if(2===this.pin_pos.length)return-this.parse_coord(this.pin_pos[1],this.h)},z_index:function(){return(this.settings["z-index"]||this.settings.zIndex||0)+(this.uxr.z_index||0)},background:function(){var t=this.uxr.background||this.$props.colors.back;return y.apply_opacity(t,this.uxr.background_opacity||this.$props.config.UX_OPACITY)},inactive_btn_color:function(){return this.uxr.inactive_btn_color||this.$props.colors.grid},wrapper:function(){return{x:this.x,y:this.y,pin_x:this.x-this.ox,pin_y:this.y-this.oy}}},watch:{updater:function(){this.update_position()}},data:function(){return{x:0,y:0,w:0,h:0,visible:!0}}},st=(n(44),Z(ot,rt,[],!1,null,null,null));st.options.__file="src/components/UxWrapper.vue";var at=Z({name:"UxLayer",props:["tv_id","id","uxs","updater","colors","config"],components:{UxWrapper:st.exports},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{on_custom_event:function(t){this.$emit("custom-event",t)}},computed:{style:function(){return{top:0!==this.$props.id?"1px":0,left:0,width:"100%",height:"calc(100% - 2px)",position:"absolute","z-index":"1","pointer-events":"none",overflow:"hidden"}}}},it,[],!1,null,null,null);at.options.__file="src/components/UxLayer.vue";var ct=at.exports;function ut(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return ht(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ht(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function ht(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var lt=function(){function t(e){Y()(this,t),this.comp=e,this.map={},this.listeners=0,this.pressed=!1,this.x=e.$props.cursor.x,this.y=e.$props.cursor.y,this.t=e.$props.cursor.t,this.y$=e.$props.cursor.y$}return U()(t,[{key:"on",value:function(t,e,n){void 0===n&&(n="unshift"),e&&(this.map[t]=this.map[t]||[],this.map[t][n](e),this.listeners++)}},{key:"off",value:function(t,e){if(this.map[t]){var n=this.map[t].indexOf(e);n<0||(this.map[t].splice(n,1),this.listeners--)}}},{key:"emit",value:function(t,e){var n=this.comp.layout;if(t in this.map){var i,r=ut(this.map[t]);try{for(r.s();!(i=r.n()).done;){(0,i.value)(e)}}catch(t){r.e(t)}finally{r.f()}}"mousemove"===t&&(this.x=e.layerX,this.y=e.layerY,this.t=n.screen2t(this.x),this.y$=n.screen2$(this.y)),"mousedown"===t&&(this.pressed=!0),"mouseup"===t&&(this.pressed=!1)}}]),t}(),ft={props:["id","num","interval","cursor","colors","layout","sub","data","settings","grid_id","font","config","meta","tf","i0"],mounted:function(){this.draw||(this.draw=function(t){console.warn("EARLY ADOPTER BUG: reload the browser & enjoy")});var t=this.$props.sub===this.$props.data;this.meta_info();try{new Function("return "+this.$emit)(),this._$emit=this.$emit,this.$emit=this.custom_event}catch(t){return}this._$emit("new-grid-layer",{name:this.$options.name,id:this.$props.id,renderer:this,display:!("display"in this.$props.settings)||this.$props.settings.display,z:this.$props.settings["z-index"]||this.$props.settings.zIndex||(t?0:-1)}),this._$emit("layer-meta-props",{grid_id:this.$props.grid_id,layer_id:this.$props.id,legend:this.legend,data_colors:this.data_colors,y_range:this.y_range}),this.exec_script(),this.mouse=new lt(this),this.init_tool&&this.init_tool(),this.init&&this.init()},beforeDestroy:function(){this.destroy&&this.destroy(),this._$emit("delete-grid-layer",this.$props.id)},methods:{use_for:function(){console.warn("use_for() should be implemented"),console.warn("Format: use_for() {\n                  return ['type1', 'type2', ...]\n            }")},meta_info:function(){var t=this.$props.id;console.warn("".concat(t," meta_info() is req. for publishing")),console.warn("Format: meta_info() {\n                author: 'Satoshi Smith',\n                version: '1.0.0',\n                contact (opt) '<email>'\n                github: (opt) '<GitHub Page>',\n            }")},custom_event:function(t){if("hook"!==t.split(":")[0]){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];"change-settings"!==t&&"object-selected"!==t&&"new-shader"!==t&&"new-interface"!==t&&"remove-tool"!==t||(n.push(this.grid_id,this.id),this.$props.settings.$uuid&&n.push(this.$props.settings.$uuid)),"new-interface"===t&&(n[0].overlay=this,n[0].uuid=this.last_ux_id="".concat(this.grid_id,"-").concat(this.id,"-").concat(this.uxs_count++)),"custom-event"!==t&&this._$emit("custom-event",{event:t,args:n})}},exec_script:function(){this.calc&&this.$emit("exec-script",{grid_id:this.$props.grid_id,layer_id:this.$props.id,src:this.calc(),use_for:this.use_for()})}},watch:{settings:{handler:function(t,e){this.watch_uuid&&this.watch_uuid(t,e),this._$emit("show-grid-layer",{id:this.$props.id,display:!("display"in this.$props.settings)||this.$props.settings.display})},deep:!0}},data:function(){return{uxs_count:0,last_ux_id:null}},render:function(t){return t()}},pt=Z({name:"Spline",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.1.2"}},draw:function(t){t.lineWidth=this.line_width,t.strokeStyle=this.color,t.beginPath();var e=this.$props.layout,n=this.data_index,i=this.$props.data;if(this.skip_nan){var r=!1;for(c=0,u=i.length;c<u;c++){var o=i[c],s=e.t2screen(o[0]),a=e.$2screen(o[n]);null==o[n]||a!=a?r=!0:(r&&t.moveTo(s,a),t.lineTo(s,a),r=!1)}}else for(var c=0,u=i.length;c<u;c++){var h=i[c],l=e.t2screen(h[0]),f=e.$2screen(h[n]);t.lineTo(l,f)}t.stroke()},use_for:function(){return["Spline","EMA","SMA"]},data_colors:function(){return[this.color]}},computed:{sett:function(){return this.$props.settings},line_width:function(){return this.sett.lineWidth||.75},color:function(){var t=this.$props.num%5;return this.sett.color||this.COLORS[t]},data_index:function(){return this.sett.dataIndex||1},skip_nan:function(){return this.sett.skipNaN}},data:function(){return{COLORS:["#42b28a","#5691ce","#612ff9","#d50b90","#ff2316"]}}},undefined,undefined,!1,null,null,null);pt.options.__file="src/components/overlays/Spline.vue";var dt=pt.exports,gt=Z({name:"Splines",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.1.0"}},draw:function(t){for(var e=0;e<this.lines_num;e++){var n=e%this.clrx.length;t.strokeStyle=this.clrx[n],t.lineWidth=this.widths[e]||this.line_width,t.beginPath(),this.draw_spline(t,e),t.stroke()}},draw_spline:function(t,e){var n=this.$props.layout,i=this.$props.data;if(this.skip_nan){var r=!1;for(c=0,u=i.length;c<u;c++){var o=i[c],s=n.t2screen(o[0]),a=n.$2screen(o[e+1]);null==o[e+1]||a!=a?r=!0:(r&&t.moveTo(s,a),t.lineTo(s,a),r=!1)}}else for(var c=0,u=i.length;c<u;c++){var h=i[c],l=n.t2screen(h[0]),f=n.$2screen(h[e+1]);t.lineTo(l,f)}},use_for:function(){return["Splines","DMI"]},data_colors:function(){return this.clrx}},computed:{sett:function(){return this.$props.settings},line_width:function(){return this.sett.lineWidth||.75},widths:function(){return this.sett.lineWidths||[]},clrx:function(){var t=this.sett.colors||[],e=this.$props.num;if(!t.length)for(var n=0;n<this.lines_num;n++)t.push(this.COLORS[(e+n)%5]);return t},lines_num:function(){return this.$props.data[0]?this.$props.data[0].length-1:0},skip_nan:function(){return this.sett.skipNaN}},data:function(){return{COLORS:["#42b28a","#5691ce","#612ff9","#d50b90","#ff2316"]}}},undefined,undefined,!1,null,null,null);gt.options.__file="src/components/overlays/Splines.vue";var vt=gt.exports,At=Z({name:"Range",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.0.1"}},draw:function(t){var e=this.$props.layout,n=e.$2screen(this.sett.upper||70),i=e.$2screen(this.sett.lower||30),r=this.$props.data;t.lineWidth=this.line_width,t.strokeStyle=this.color,t.beginPath();for(var o=0,s=r.length;o<s;o++){var a=r[o],c=e.t2screen(a[0]),u=e.$2screen(a[1]);t.lineTo(c,u)}t.stroke(),t.strokeStyle=this.band_color,t.setLineDash([5]),t.beginPath(),t.fillStyle=this.back_color,t.fillRect(0,n,e.width,i-n),t.moveTo(0,n),t.lineTo(e.width,n),t.moveTo(0,i),t.lineTo(e.width,i),t.stroke()},use_for:function(){return["Range","RSI"]},data_colors:function(){return[this.color]},y_range:function(t,e){return[Math.max(t,this.sett.upper||70),Math.min(e,this.sett.lower||30)]}},computed:{sett:function(){return this.$props.settings},line_width:function(){return this.sett.lineWidth||.75},color:function(){return this.sett.color||"#ec206e"},band_color:function(){return this.sett.bandColor||"#ddd"},back_color:function(){return this.sett.backColor||"#381e9c16"}}},undefined,undefined,!1,null,null,null);At.options.__file="src/components/overlays/Range.vue";var mt=At.exports,yt=Z({name:"Trades",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.0.2"}},draw:function(t){var e=this.$props.layout,n=this.$props.data;t.lineWidth=1.5,t.strokeStyle="black";for(var i=0,r=n.length;i<r;i++){var o=n[i];t.fillStyle=o[1]?this.buy_color:this.sell_color,t.beginPath();var s=e.t2screen(o[0]),a=e.$2screen(o[2]);t.arc(s,a,this.marker_size+.5,0,2*Math.PI,!0),t.fill(),t.stroke(),this.show_label&&o[3]&&this.draw_label(t,s,a,o)}},draw_label:function(t,e,n,i){t.fillStyle=this.label_color,t.font=this.new_font,t.textAlign="center",t.fillText(i[3],e,n-25)},use_for:function(){return["Trades"]},legend:function(t){switch(t[1]){case 0:var e="Sell";break;case 1:e="Buy";break;default:e="Unknown"}return[{value:e},{value:t[2].toFixed(4),color:this.$props.colors.text}].concat(t[3]?[{value:t[3]}]:[])}},computed:{sett:function(){return this.$props.settings},default_font:function(){return"12px "+this.$props.font.split("px").pop()},buy_color:function(){return this.sett.buyColor||"#63df89"},sell_color:function(){return this.sett.sellColor||"#ec4662"},label_color:function(){return this.sett.labelColor||"#999"},marker_size:function(){return this.sett.markerSize||5},show_label:function(){return!1!==this.sett.showLabel},new_font:function(){return this.sett.font||this.default_font}}},undefined,undefined,!1,null,null,null);yt.options.__file="src/components/overlays/Trades.vue";var _t=yt.exports,wt=Z({name:"Channel",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.0.1"}},draw:function(t){var e=this.data,n=this.layout;t.beginPath(),t.fillStyle=this.back_color;for(var i=0;i<e.length;i++){var r=e[i],o=n.t2screen(r[0]),s=n.$2screen(r[1]||void 0);t.lineTo(o,s)}for(i=e.length-1;i>=0;i--){var a=e[i],c=n.t2screen(a[0]),u=n.$2screen(a[3]||void 0);t.lineTo(c,u)}t.fill(),t.lineWidth=this.line_width,t.strokeStyle=this.color,t.beginPath();for(i=0;i<e.length;i++){var h=e[i],l=n.t2screen(h[0]),f=n.$2screen(h[1]||void 0);t.lineTo(l,f)}t.stroke(),t.beginPath();for(i=0;i<e.length;i++){var p=e[i],d=n.t2screen(p[0]),g=n.$2screen(p[3]||void 0);t.lineTo(d,g)}if(t.stroke(),this.show_mid){t.beginPath();for(i=0;i<e.length;i++){var v=e[i],A=n.t2screen(v[0]),m=n.$2screen(v[2]||void 0);t.lineTo(A,m)}t.stroke()}},mapp:function(t){var e=this.$props.layout;return t&&{x:e.t2screen(t[0]),y1:e.$2screen(t[1]),y2:e.$2screen(t[2]),y3:e.$2screen(t[3])}},use_for:function(){return["Channel","KC","BB"]},data_colors:function(){return[this.color,this.color,this.color]}},computed:{sett:function(){return this.$props.settings},line_width:function(){return this.sett.lineWidth||.75},color:function(){var t=this.$props.num%5;return this.sett.color||this.COLORS[t]},show_mid:function(){return!("showMid"in this.sett)||this.sett.showMid},back_color:function(){return this.sett.backColor||this.color+"11"}},data:function(){return{COLORS:["#42b28a","#5691ce","#612ff9","#d50b90","#ff2316"]}}},undefined,undefined,!1,null,null,null);wt.options.__file="src/components/overlays/Channel.vue";var bt=wt.exports,xt=Z({name:"Segment",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.0.0"}},draw:function(t){if(this.p1&&this.p2){t.lineWidth=this.line_width,t.strokeStyle=this.color,t.beginPath();var e=this.$props.layout,n=e.t2screen(this.p1[0]),i=e.$2screen(this.p1[1]);t.moveTo(n,i);var r=e.t2screen(this.p2[0]),o=e.$2screen(this.p2[1]);t.lineTo(r,o),t.stroke()}},use_for:function(){return["Segment"]},data_colors:function(){return[this.color]}},computed:{sett:function(){return this.$props.settings},p1:function(){return this.$props.settings.p1},p2:function(){return this.$props.settings.p2},line_width:function(){return this.sett.lineWidth||.9},color:function(){var t=this.$props.num%5;return this.sett.color||this.COLORS[t]}},data:function(){return{COLORS:["#42b28a","#5691ce","#612ff9","#d50b90","#ff2316"]}}},undefined,undefined,!1,null,null,null);xt.options.__file="src/components/overlays/Segment.vue";var kt=xt.exports;function Ct(t){for(var e,n,i,r=t.$props,s=r.data,a=r.layout.t2screen,c=r.layout,u=[],h=[],l=Math.max.apply(Math,o()(s.map((function(t){return t[5]})))),f=r.config.VOLSCALE*c.height/l,p=void 0,g=Bt(c,r,s),v=d()(g,2),A=v[0],m=v[1],y=c.px_step*m,_=y>5?1:0,w=0;w<s.length;w++){var b=s[w];i=a(b[0])+1,s[w-1]&&b[0]-s[w-1][0]>A&&(p=null),e=p||Math.floor(i-.5*y),n=Math.floor(i+.5*y)-.5,u.push({x:i,w:c.px_step*r.config.CANDLEW*m,o:Math.floor(b[1]*c.A+c.B),h:Math.floor(b[2]*c.A+c.B),l:Math.floor(b[3]*c.A+c.B),c:Math.floor(b[4]*c.A+c.B),raw:b}),h.push({x1:e,x2:n,h:b[5]*f,green:b[4]>=b[1],raw:b}),p=n+_}return{candles:u,volume:h}}function It(t){var e=t.$props,n=e.data,i=e.layout.t2screen,r=e.layout,s=[],a=n[0]?n[0].length:0;t._i1=a<6?1:5,t._i2=a<6?function(t){return t[2]}:function(t){return t[4]>=t[1]};for(var c,u,h,l=Math.max.apply(Math,o()(n.map((function(e){return e[t._i1]})))),f=(t.volscale||e.config.VOLSCALE)*r.height/l,p=void 0,g=Bt(r,e,n),v=d()(g,2),A=v[0],m=v[1],y=r.px_step*m,_=y>5?1:0,w=0;w<n.length;w++){var b=n[w];h=i(b[0])+1,n[w-1]&&b[0]-n[w-1][0]>A&&(p=null),c=p||Math.floor(h-.5*y),u=Math.floor(h+.5*y)-.5,s.push({x1:c,x2:u,h:b[t._i1]*f,green:t._i2(b),raw:b}),p=u+_}return s}function Bt(t,e,n){if(t.ti_map.ib)if(e.tf)i=r=e.tf/t.ti_map.tf;else r=(i=y.detect_interval(n))/e.interval;else var i,r=(i=e.tf||y.detect_interval(n))/e.interval;return[i,r]}var Et=function(){function t(e,n,i){Y()(this,t),this.ctx=n,this.self=e,this.style=i.raw[6]||this.self,this.draw(i)}return U()(t,[{key:"draw",value:function(t){var e=t.raw[4]>=t.raw[1],n=e?this.style.colorCandleUp:this.style.colorCandleDw,i=e?this.style.colorWickUp:this.style.colorWickDw,r=Math.max(t.w,1),o=Math.max(Math.floor(.5*r),1),s=Math.abs(t.o-t.c),a=t.c===t.o?1:2,c=Math.floor(t.x)-.5;if(this.ctx.strokeStyle=i,this.ctx.beginPath(),this.ctx.moveTo(c,Math.floor(t.h)),this.ctx.lineTo(c,Math.floor(t.l)),this.ctx.stroke(),t.w>1.5){this.ctx.fillStyle=n;var u=e?1:-1;this.ctx.fillRect(Math.floor(t.x-o-1),t.c,Math.floor(2*o+1),u*Math.max(s,a))}else this.ctx.strokeStyle=n,this.ctx.beginPath(),this.ctx.moveTo(c,Math.floor(Math.min(t.o,t.c))),this.ctx.lineTo(c,Math.floor(Math.max(t.o,t.c))+(t.o===t.c?1:0)),this.ctx.stroke()}}]),t}(),St=function(){function t(e,n,i){Y()(this,t),this.ctx=n,this.$p=e.$props,this.self=e,this.style=i.raw[6]||this.self,this.draw(i)}return U()(t,[{key:"draw",value:function(t){var e=this.$p.layout.height,n=t.x2-t.x1,i=Math.floor(t.h);this.ctx.fillStyle=t.green?this.style.colorVolUp:this.style.colorVolDw,this.ctx.fillRect(Math.floor(t.x1),Math.floor(e-i-.5),Math.floor(n),Math.floor(i+1))}}]),t}(),Tt=function(){function t(e){Y()(this,t),this.comp=e,this.data=e.$props.data}return U()(t,[{key:"init_shader",value:function(){var t=this,e=this.comp.$props.layout,n=this.comp.$props.config,i=this.comp,r=function(){return t.last_bar()};this.comp.$emit("new-shader",{target:"sidebar",draw:function(t){if(r()){var o=r(),s=t.canvas.width,a=n.PANHEIGHT,c=o.price.toFixed(e.prec);t.fillStyle=o.color;var u=o.y-.5*a-.5;t.fillRect(-1,u,s+1,a),t.fillStyle=i.$props.colors.textHL,t.textAlign="left",t.fillText(c,7,u+15)}}}),this.shader=!0}},{key:"draw",value:function(t){if(this.comp.$props.meta.last){this.shader||this.init_shader();var e=this.comp.$props.layout,n=this.comp.$props.meta.last,i=n[4]>=n[1],r=i?this.green():this.red(),o=e.$2screen(n[4])+(i?1:0);t.strokeStyle=r,t.setLineDash([1,1]),t.beginPath(),t.moveTo(0,o),t.lineTo(e.width,o),t.stroke(),t.setLineDash([])}}},{key:"last_bar",value:function(){if(this.data.length){var t=this.comp.$props.layout,e=this.data[this.data.length-1],n=(t.$2screen(e[4]),t.c_magnet(e[0]));return{y:Math.floor(n.c)-.5,price:e[4],color:e[4]>=e[1]?this.green():this.red()}}}},{key:"last_price",value:function(){return this.comp.$props.meta.last?this.comp.$props.meta.last[4]:void 0}},{key:"green",value:function(){return this.comp.colorCandleUp}},{key:"red",value:function(){return this.comp.colorCandleDw}}]),t}(),Mt=Z({name:"Candles",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.2.1"}},init:function(){this.price=new Tt(this)},draw:function(t){if(this.$props.sub===this.$props.data)var e={candles:this.$props.layout.candles,volume:this.$props.layout.volume};else e=Ct(this);if(this.show_volume)for(var n=e.volume,i=0,r=n.length;i<r;i++)new St(this,t,n[i]);var o=e.candles;for(i=0,r=o.length;i<r;i++)new Et(this,t,o[i]);this.price_line&&this.price.draw(t)},use_for:function(){return["Candles"]},y_range:function(){for(var t=-1/0,e=1/0,n=0,i=this.sub.length;n<i;n++){var r=this.sub[n];r[2]>t&&(t=r[2]),r[3]<e&&(e=r[3])}return[t,e]}},computed:{sett:function(){return this.$props.settings},show_volume:function(){return!("showVolume"in this.sett)||this.sett.showVolume},price_line:function(){return!("priceLine"in this.sett)||this.sett.priceLine},colorCandleUp:function(){return this.sett.colorCandleUp||this.$props.colors.candleUp},colorCandleDw:function(){return this.sett.colorCandleDw||this.$props.colors.candleDw},colorWickUp:function(){return this.sett.colorWickUp||this.$props.colors.wickUp},colorWickDw:function(){return this.sett.colorWickDw||this.$props.colors.wickDw},colorWickSm:function(){return this.sett.colorWickSm||this.$props.colors.wickSm},colorVolUp:function(){return this.sett.colorVolUp||this.$props.colors.volUp},colorVolDw:function(){return this.sett.colorVolDw||this.$props.colors.volDw}},data:function(){return{price:{}}}},undefined,undefined,!1,null,null,null);Mt.options.__file="src/components/overlays/Candles.vue";var Ot=Mt.exports;function Qt(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Dt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Dt(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Dt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var Gt=Z({name:"Volume",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.1.0"}},draw:function(t){var e,n=Qt(It(this));try{for(n.s();!(e=n.n()).done;){var i=e.value;new St(this,t,i)}}catch(t){n.e(t)}finally{n.f()}},use_for:function(){return["Volume"]},legend:function(t){var e=(this._i2?this._i2(t):t[2])?this.colorVolUpLegend:this.colorVolDwLegend;return[{value:t[this._i1||1],color:e}]},y_range:function(t,e){var n=this;if(5===this._i1){var i=this.$props.sub;return[Math.max.apply(Math,o()(i.map((function(t){return t[n._i1]})))),Math.min.apply(Math,o()(i.map((function(t){return t[n._i1]}))))]}return[t,e]}},computed:{sett:function(){return this.$props.settings},colorVolUp:function(){return this.sett.colorVolUp||this.$props.colors.volUp},colorVolDw:function(){return this.sett.colorVolDw||this.$props.colors.volDw},colorVolUpLegend:function(){return this.sett.colorVolUpLegend||this.$props.colors.candleUp},colorVolDwLegend:function(){return this.sett.colorVolDwLegend||this.$props.colors.candleDw},volscale:function(){return this.sett.volscale||this.$props.grid_id>0?.85:this.$props.config.VOLSCALE}},data:function(){return{}}},undefined,undefined,!1,null,null,null);Gt.options.__file="src/components/overlays/Volume.vue";var Yt=Gt.exports,jt=Z({name:"Splitters",mixins:[ft],methods:{meta_info:function(){return{author:"C451",version:"1.0.1"}},draw:function(t){var e=this,n=this.$props.layout;t.lineWidth=this.line_width,t.strokeStyle=this.line_color,this.$props.data.forEach((function(i,r){t.beginPath();var o=n.t2screen(i[0]);t.setLineDash([10,10]),t.moveTo(o,0),t.lineTo(o,e.layout.height),t.stroke(),i[1]&&e.draw_label(t,o,i)}))},draw_label:function(t,e,n){var i=n[2]?1:-1;e+=2.5*i,t.font=this.new_font;var r=n[4]||this.y_position,o=t.measureText(n[1]).width+10,s=this.layout.height*(1-r);s=Math.floor(s),t.fillStyle=n[3]||this.flag_color,t.beginPath(),t.moveTo(e,s),t.lineTo(e+10*i,s-10*i),t.lineTo(e+(o+10)*i,s-10*i),t.lineTo(e+(o+10)*i,s+10*i),t.lineTo(e+10*i,s+10*i),t.closePath(),t.fill(),t.fillStyle=this.label_color,t.textAlign=i<0?"right":"left",t.fillText(n[1],e+15*i,s+4)},use_for:function(){return["Splitters"]}},computed:{sett:function(){return this.$props.settings},new_font:function(){return this.sett.font||"12px "+this.$props.font.split("px").pop()},flag_color:function(){return this.sett.flagColor||"#4285f4"},label_color:function(){return this.sett.labelColor||"#fff"},line_color:function(){return this.sett.lineColor||"#4285f4"},line_width:function(){return this.sett.lineWidth||1},y_position:function(){return this.sett.yPosition||.9}},data:function(){return{}}},undefined,undefined,!1,null,null,null);jt.options.__file="src/components/overlays/Splitters.vue";var Ut=jt.exports;function Rt(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Ft(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ft(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Ft(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var Lt=function(){function t(e){Y()(this,t),this.comp=e,this.map={},this.listeners=0,this.keymap={}}return U()(t,[{key:"on",value:function(t,e){e&&(this.map[t]=this.map[t]||[],this.map[t].push(e),this.listeners++)}},{key:"emit",value:function(t,e){if(t in this.map){var n,i=Rt(this.map[t]);try{for(i.s();!(n=i.n()).done;){(0,n.value)(e)}}catch(t){i.e(t)}finally{i.f()}}"keydown"===t&&(this.keymap[e.key]||this.emit(e.key),this.keymap[e.key]=!0),"keyup"===t&&(this.keymap[e.key]=!1)}},{key:"pressed",value:function(t){return this.keymap[t]}}]),t}();function Nt(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Pt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Pt(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var Kt={methods:{init_tool:function(){var t=this;this.collisions=[],this.pins=[],this.mouse.on("mousemove",(function(e){t.collisions.some((function(e){return e(t.mouse.x,t.mouse.y)}))?t.show_pins=!0:t.show_pins=!1,t.drag&&t.drag_update()})),this.mouse.on("mousedown",(function(e){y.default_prevented(e)||t.collisions.some((function(e){return e(t.mouse.x,t.mouse.y)}))&&(t.selected||t.$emit("object-selected"),t.start_drag(),e.preventDefault(),t.pins.forEach((function(t){return t.mousedown(e,!0)})))})),this.mouse.on("mouseup",(function(e){t.drag=null,t.$emit("scroll-lock",!1)})),this.keys=new Lt(this),this.keys.on("Delete",this.remove_tool),this.keys.on("Backspace",this.remove_tool),this.show_pins=!1,this.drag=null},render_pins:function(t){(this.selected||this.show_pins)&&this.pins.forEach((function(e){return e.draw(t)}))},set_state:function(t){this.$emit("change-settings",{$state:t})},watch_uuid:function(t,e){if(t.$uuid!==e.$uuid){var n,i=Nt(this.pins);try{for(i.s();!(n=i.n()).done;){(e=n.value).re_init()}}catch(t){i.e(t)}finally{i.f()}this.collisions=[],this.show_pins=!1,this.drag=null}},pre_draw:function(){this.collisions=[]},remove_tool:function(){this.selected&&this.$emit("remove-tool")},start_drag:function(){this.$emit("scroll-lock",!0);var t=this.$props.cursor;this.drag={t:t.t,y$:t.y$},this.pins.forEach((function(t){return t.rec_position()}))},drag_update:function(){var t=this.$props.cursor.t-this.drag.t,e=this.$props.cursor.y$-this.drag.y$;this.pins.forEach((function(n){return n.update_from([n.t1+t,n.y$1+e],!0)}))}},computed:{selected:function(){return this.$props.settings.$selected},state:function(){return this.$props.settings.$state}}},Ht=n(6),zt=n(14),Wt=n.n(zt),Jt=function(){function t(e,n,i){var r=this;void 0===i&&(i={}),Y()(this,t),this.RADIUS=e.$props.config.PIN_RADIUS||5.5,this.RADIUS_SQ=Math.pow(this.RADIUS+7,2),y.is_mobile&&(this.RADIUS+=2,this.RADIUS_SQ*=2.5),this.COLOR_BACK=e.$props.colors.back,this.COLOR_BR=e.$props.colors.text,this.comp=e,this.layout=e.layout,this.mouse=e.mouse,this.name=n,this.state=i.state||"settled",this.hidden=i.hidden||!1,this.mouse.on("mousemove",(function(t){return r.mousemove(t)})),this.mouse.on("mousedown",(function(t){return r.mousedown(t)})),this.mouse.on("mouseup",(function(t){return r.mouseup(t)})),"finished"===e.state?(this.state="settled",this.update_from(e.$props.settings[n])):this.update(),"settled"!==this.state&&this.comp.$emit("scroll-lock",!0)}return U()(t,[{key:"re_init",value:function(){this.update_from(this.comp.$props.settings[this.name])}},{key:"draw",value:function(t){if(!this.hidden)switch(this.state){case"tracking":break;case"dragging":this.moved||this.draw_circle(t);break;case"settled":this.draw_circle(t)}}},{key:"draw_circle",value:function(t){if(this.layout=this.comp.layout,this.comp.selected)var e=this.RADIUS,n=1.5;else e=.95*this.RADIUS,n=1;t.lineWidth=n,t.strokeStyle=this.COLOR_BR,t.fillStyle=this.COLOR_BACK,t.beginPath(),t.arc(this.x=this.layout.t2screen(this.t),this.y=this.layout.$2screen(this.y$),e+.5,0,2*Math.PI,!0),t.fill(),t.stroke()}},{key:"update",value:function(){this.y$=this.comp.$props.cursor.y$,this.y=this.comp.$props.cursor.y,this.t=this.comp.$props.cursor.t,this.x=this.comp.$props.cursor.x,this.comp.$emit("change-settings",Wt()({},this.name,[this.t,this.y$]))}},{key:"update_from",value:function(t,e){void 0===e&&(e=!1),t&&(this.layout=this.comp.layout,this.y$=t[1],this.y=this.layout.$2screen(this.y$),this.t=t[0],this.x=this.layout.t2screen(this.t),e&&this.comp.$emit("change-settings",Wt()({},this.name,[this.t,this.y$])))}},{key:"rec_position",value:function(){this.t1=this.t,this.y$1=this.y$}},{key:"mousemove",value:function(t){switch(this.state){case"tracking":case"dragging":this.moved=!0,this.update()}}},{key:"mousedown",value:function(t,e){if(void 0===e&&(e=!1),!y.default_prevented(t)||e){switch(this.state){case"tracking":this.state="settled",this.on_settled&&this.on_settled(),this.comp.$emit("scroll-lock",!1);break;case"settled":if(this.hidden)return;this.hover()&&(this.state="dragging",this.moved=!1,this.comp.$emit("scroll-lock",!0),this.comp.$emit("object-selected"))}this.hover()&&t.preventDefault()}}},{key:"mouseup",value:function(t){switch(this.state){case"dragging":this.state="settled",this.on_settled&&this.on_settled(),this.comp.$emit("scroll-lock",!1)}}},{key:"on",value:function(t,e){switch(t){case"settled":this.on_settled=e}}},{key:"hover",value:function(){var t=this.x,e=this.y;return(t-this.mouse.x)*(t-this.mouse.x)+(e-this.mouse.y)*(e-this.mouse.y)<this.RADIUS_SQ}}]),t}(),$t=function(){function t(e,n){Y()(this,t),this.ctx=n,this.comp=e,this.T=e.$props.config.TOOL_COLL,y.is_mobile&&(this.T*=2)}return U()(t,[{key:"draw",value:function(t,e){var n=this.comp.$props.layout,i=n.t2screen(t[0]),r=n.$2screen(t[1]),o=n.t2screen(e[0]),s=n.$2screen(e[1]);this.ctx.moveTo(i,r),this.ctx.lineTo(o,s),this.comp.collisions.push(this.make([i,r],[o,s]))}},{key:"make",value:function(t,e){var n=this;return function(i,r){return _.point2seg([i,r],t,e)<n.T}}}]),t}(),Vt=function(){function t(e,n){Y()(this,t),this.ctx=n,this.comp=e,this.T=e.$props.config.TOOL_COLL,y.is_mobile&&(this.T*=2)}return U()(t,[{key:"draw",value:function(t,e){var n=this.comp.$props.layout,i=n.t2screen(t[0]),r=n.$2screen(t[1]),o=n.t2screen(e[0]),s=n.$2screen(e[1]);this.ctx.moveTo(i,r),this.ctx.lineTo(o,s);var a=n.width,c=n.height,u=(s-r)/(o-i),h=Math.sign(o-i||s-r),l=a*h*2,f=a*u*h*2;f===1/0&&(l=0,f=c*h),this.ctx.moveTo(o,s),this.ctx.lineTo(o+l,s+f),this.ray||(this.ctx.moveTo(i,r),this.ctx.lineTo(i-l,r-f)),this.comp.collisions.push(this.make([i,r],[o,s]))}},{key:"make",value:function(t,e){var n=this,i=this.ray?_.point2ray.bind(_):_.point2line.bind(_);return function(r,o){return i([r,o],t,e)<n.T}}}]),t}(),Xt=n(12),Zt=n.n(Xt),qt=n(13),te=n.n(qt),ee=n(9),ne=n.n(ee);function ie(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=ne()(t);if(e){var r=ne()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return te()(this,n)}}var re=function(t){Zt()(n,t);var e=ie(n);function n(t,i){var r;return Y()(this,n),(r=e.call(this,t,i)).ray=!0,r}return n}(Vt),oe=Z({name:"LineTool",mixins:[ft,Kt],methods:{meta_info:function(){return{author:"C451",version:"1.1.0"}},tool:function(){return{group:"Lines",icon:Ht["segment.png"],type:"Segment",hint:"This hint will be shown on hover",data:[],settings:{},mods:{Extended:{settings:{extended:!0},icon:Ht["extended.png"]},Ray:{settings:{ray:!0},icon:Ht["ray.png"]}}}},init:function(){var t=this;this.pins.push(new Jt(this,"p1")),this.pins.push(new Jt(this,"p2",{state:"tracking"})),this.pins[1].on("settled",(function(){t.set_state("finished"),t.$emit("drawing-mode-off")}))},draw:function(t){this.p1&&this.p2&&(t.lineWidth=this.line_width,t.strokeStyle=this.color,t.beginPath(),this.sett.ray?new re(this,t).draw(this.p1,this.p2):this.sett.extended?new Vt(this,t).draw(this.p1,this.p2):new $t(this,t).draw(this.p1,this.p2),t.stroke(),this.render_pins(t))},use_for:function(){return["LineTool"]},data_colors:function(){return[this.color]}},computed:{sett:function(){return this.$props.settings},p1:function(){return this.$props.settings.p1},p2:function(){return this.$props.settings.p2},line_width:function(){return this.sett.lineWidth||.9},color:function(){return this.sett.color||"#42b28a"}},data:function(){return{}}},undefined,undefined,!1,null,null,null);oe.options.__file="src/components/overlays/LineTool.vue";var se=oe.exports,ae=Z({name:"RangeTool",mixins:[ft,Kt],methods:{meta_info:function(){return{author:"C451",version:"2.0.1"}},tool:function(){return{group:"Measurements",icon:Ht["price_range.png"],type:"Price",hint:"Price Range",data:[],settings:{},mods:{Time:{icon:Ht["time_range.png"],settings:{price:!1,time:!0}},PriceTime:{icon:Ht["price_time.png"],settings:{price:!0,time:!0}},ShiftMode:{settings:{price:!0,time:!0,shiftMode:!0},hidden:!0}}}},init:function(){var t=this;this.pins.push(new Jt(this,"p1",{hidden:this.shift})),this.pins.push(new Jt(this,"p2",{state:"tracking",hidden:this.shift})),this.pins[1].on("settled",(function(){t.set_state("finished"),t.$emit("drawing-mode-off"),t.shift&&t._$emit("custom-event",{event:"object-selected",args:[]})}))},draw:function(t){if(this.p1&&this.p2){var e=Math.sign(this.p2[1]-this.p1[1]),n=this.$props.layout,i=n.t2screen(.5*(this.p1[0]+this.p2[0]));t.lineWidth=this.line_width,t.strokeStyle=this.color,t.fillStyle=this.back_color;var r=n.t2screen(this.p1[0]),o=n.$2screen(this.p1[1]),s=n.t2screen(this.p2[0]),a=n.$2screen(this.p2[1]);t.fillRect(r,o,s-r,a-o),this.price&&this.vertical(t,r,o,s,a,i),this.time&&this.horizontal(t,r,o,s,a,i),this.draw_value(t,e,i,a),this.render_pins(t)}},vertical:function(t,e,n,i,r,o){this.$props.layout;var s=Math.sign(this.p2[1]-this.p1[1]);t.beginPath(),this.shift||(new $t(this,t).draw([this.p1[0],this.p2[1]],[this.p2[0],this.p2[1]]),new $t(this,t).draw([this.p1[0],this.p1[1]],[this.p2[0],this.p1[1]])),t.moveTo(o-4,r+5*s),t.lineTo(o,r),t.lineTo(o+4,r+5*s),t.stroke(),t.beginPath(),t.setLineDash([5,5]),new $t(this,t).draw([.5*(this.p1[0]+this.p2[0]),this.p2[1]],[.5*(this.p1[0]+this.p2[0]),this.p1[1]]),t.stroke(),t.setLineDash([])},horizontal:function(t,e,n,i,r,o){var s=this.$props.layout,a=Math.sign(this.p2[0]-this.p1[0]),c=(s.$2screen(this.p1[1])+s.$2screen(this.p2[1]))/2;t.beginPath(),this.shift||(new $t(this,t).draw([this.p1[0],this.p1[1]],[this.p1[0],this.p2[1]]),new $t(this,t).draw([this.p2[0],this.p1[1]],[this.p2[0],this.p2[1]])),t.moveTo(i-5*a,c-4),t.lineTo(i,c),t.lineTo(i-5*a,c+4),t.stroke(),t.beginPath(),t.setLineDash([5,5]),t.moveTo(e,c),t.lineTo(i,c),t.stroke(),t.setLineDash([])},draw_value:function(t,e,n,i){var r=this;t.font=this.new_font;var s=(this.p2[1]-this.p1[1]).toFixed(this.prec),a=(100*(this.p2[1]/this.p1[1]-1)).toFixed(this.prec),c=function(t){return r.layout.ti_map.smth2t(t)},u=c(this.p2[0])-c(this.p1[0]),h=(this.layout.ti_map.tf,function(t){var e=r.layout.c_magnet(t);return(r.layout.candles||r.layout.master_grid.candles).indexOf(e)}),l=h(this.p2[0])-h(this.p1[0]),f=this.t2str(u),p=[];this.price&&p.push("".concat(s,"  (").concat(a,"%)")),this.time&&p.push("".concat(l," bars, ").concat(f));var d=(p=p.join("\n")).split("\n"),g=Math.max.apply(Math,o()(d.map((function(e){return t.measureText(e).width+20}))).concat([100])),v=d.length,A=20*v;t.fillStyle=this.value_back,t.fillRect(n-.5*g,i-(10+A)*e,g,A*e),t.fillStyle=this.value_color,t.textAlign="center",d.forEach((function(r,o){t.fillText(r,n,i+(e>0?20*o-20*v+5:20*o+25))}))},t2str:function(t){for(var e=Math.sign(t),n=Math.abs(t),i=[[1e3,"s",60],[6e4,"m",60],[36e5,"h",24],[864e5,"D",7],[6048e5,"W",4],[2592e6,"M",12],[31536e6,"Y",1/0],[1/0,"Eternity",1/0]],r=0;r<i.length;r++)if(i[r][0]=Math.floor(n/i[r][0]),0===i[r][0]){var o=i[r-1],s=i[r-2],a=e<0?"-":"";o&&(a+=o.slice(0,2).join(""));var c=s?s[0]-o[0]*s[2]:0;return s&&c&&(a+=" ",a+="".concat(c).concat(s[1])),a}},use_for:function(){return["RangeTool"]},data_colors:function(){return[this.color]}},computed:{sett:function(){return this.$props.settings},p1:function(){return this.$props.settings.p1},p2:function(){return this.$props.settings.p2},line_width:function(){return this.sett.lineWidth||.9},color:function(){return this.sett.color||this.$props.colors.cross},back_color:function(){return this.sett.backColor||"#9b9ba316"},value_back:function(){return this.sett.valueBack||"#9b9ba316"},value_color:function(){return this.sett.valueColor||this.$props.colors.text},prec:function(){return this.sett.precision||2},new_font:function(){return"12px "+this.$props.font.split("px").pop()},price:function(){return!("price"in this.sett)||this.sett.price},time:function(){return"time"in this.sett&&this.sett.time},shift:function(){return this.sett.shiftMode}},data:function(){return{}}},undefined,undefined,!1,null,null,null);ae.options.__file="src/components/overlays/RangeTool.vue";var ce=ae.exports;function ue(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return he(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return he(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function he(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var le=Z({name:"Grid",props:["sub","layout","range","interval","cursor","colors","overlays","width","height","data","grid_id","y_transform","font","tv_id","config","meta","shaders"],mixins:[V,{methods:{on_ux_event:function(t,e){if("new-interface"===t.event)t.args[0].target===e&&(t.args[0].vars=t.args[0].vars||{},t.args[0].grid_id=t.args[1],t.args[0].overlay_id=t.args[2],this.uxs.push(t.args[0]));else if("close-interface"===t.event)this.uxs=this.uxs.filter((function(e){return e.uuid!==t.args[0]}));else if("modify-interface"===t.event){var n=this.uxs.filter((function(e){return e.uuid===t.args[0]}));n.length&&this.modify(n[0],t.args[1])}else if("hide-interface"===t.event){var i=this.uxs.filter((function(e){return e.uuid===t.args[0]}));i.length&&(i[0].hidden=!0,this.modify(i[0],{hidden:!0}))}else{if("show-interface"!==t.event)return t;var r=this.uxs.filter((function(e){return e.uuid===t.args[0]}));r.length&&this.modify(r[0],{hidden:!1})}},modify:function(t,e){for(var n in void 0===e&&(e={}),e)n in t&&this.$set(t,n,e[n])},remove_all_ux:function(t){this.uxs=this.uxs.filter((function(e){return e.overlay.id!==t}))}},data:function(){return{uxs:[]}}}],components:{Crosshair:tt,KeyboardListener:nt},created:function(){var t=this;this._list=[dt,vt,mt,_t,bt,kt,Ot,Yt,Ut,se,ce].concat(this.$props.overlays),this._registry={};var e=[];this._list.forEach((function(n,i){var r=n.methods.use_for();n.methods.tool&&e.push({use_for:r,info:n.methods.tool()}),r.forEach((function(e){t._registry[e]=i}))})),this.$emit("custom-event",{event:"register-tools",args:e}),this.$on("custom-event",(function(e){return t.on_ux_event(e,"grid")}))},beforeDestroy:function(){this.renderer&&this.renderer.destroy()},mounted:function(){var t=this,e=this.$refs.canvas;this.renderer=new $(e,this),this.setup(),this.$nextTick((function(){return t.redraw()}))},render:function(t){var e=this.$props.grid_id,n=this.$props.layout.grids[e];return this.create_canvas(t,"grid-".concat(e),{position:{x:0,y:n.offset||0},attrs:{width:n.width,height:n.height,overflow:"hidden"},style:{backgroundColor:this.$props.colors.back},hs:[t(tt,{props:this.common_props(),on:this.layer_events}),t(nt,{on:this.keyboard_events}),t(ct,{props:{id:e,tv_id:this.$props.tv_id,uxs:this.uxs,colors:this.$props.colors,config:this.$props.config,updater:Math.random()},on:{"custom-event":this.emit_ux_event}})].concat(this.get_overlays(t))})},methods:{new_layer:function(t){var e=this;this.$nextTick((function(){return e.renderer.new_layer(t)}))},del_layer:function(t){var e=this;this.$nextTick((function(){return e.renderer.del_layer(t)}));var n=this.$props.grid_id;this.$emit("custom-event",{event:"remove-shaders",args:[n,t]}),this.$emit("custom-event",{event:"remove-layer-meta",args:[n,t]}),this.remove_all_ux(t)},get_overlays:function(t){var e,n=this,i=[],r={},o=ue(this.$props.data);try{for(o.s();!(e=o.n()).done;){var s=e.value,a=this._list[this._registry[s.type]];a&&(a.methods.calc&&(a=this.inject_renderer(a)),i.push({cls:a,type:s.type,data:s.data,settings:s.settings,i0:s.i0,tf:s.tf}),r[s.type]=0)}}catch(t){o.e(t)}finally{o.f()}return i.map((function(e,i){return t(e.cls,{on:n.layer_events,attrs:Object.assign(n.common_props(),{id:"".concat(e.type,"_").concat(r[e.type]++),type:e.type,data:e.data,settings:e.settings,i0:e.i0,tf:e.tf,num:i,grid_id:n.$props.grid_id,meta:n.$props.meta})})}))},common_props:function(){return{cursor:this.$props.cursor,colors:this.$props.colors,layout:this.$props.layout.grids[this.$props.grid_id],interval:this.$props.interval,sub:this.$props.sub,font:this.$props.font,config:this.$props.config}},emit_ux_event:function(t){this.on_ux_event(t,"grid")&&this.$emit("custom-event",t)},inject_renderer:function(t){var e=t.methods.calc();if(!e.conf||!e.conf.renderer||t.__renderer__)return t;var n=this._list.find((function(t){return t.name===e.conf.renderer}));return n?(t.mixins.push(n),t.__renderer__=e.conf.renderer,t):t}},computed:{is_active:function(){return void 0!==this.$props.cursor.t&&this.$props.cursor.grid_id===this.$props.grid_id}},watch:{range:{handler:function(){var t=this;this.$nextTick((function(){return t.redraw()}))},deep:!0},cursor:{handler:function(){this.$props.cursor.locked||this.redraw()},deep:!0},overlays:{handler:function(t){var e,n=ue(t);try{for(n.s();!(e=n.n()).done;){var i,r=e.value,o=ue(this.$children);try{for(o.s();!(i=o.n()).done;){var s=i.value;if("string"==typeof s.id){var a=s.id.split("_");if(a.pop(),a.join("_")===r.name){if(s.calc=r.methods.calc,!s.calc)continue;var c=s.calc.toString();c!==r.__prevscript__&&s.exec_script(),r.__prevscript__=c}}}}catch(t){o.e(t)}finally{o.f()}}}catch(t){n.e(t)}finally{n.f()}},deep:!0},shaders:function(t,e){this.redraw()}},data:function(){var t=this;return{layer_events:{"new-grid-layer":this.new_layer,"delete-grid-layer":this.del_layer,"show-grid-layer":function(e){t.renderer.show_hide_layer(e),t.redraw()},"redraw-grid":this.redraw,"layer-meta-props":function(e){return t.$emit("layer-meta-props",e)},"custom-event":function(e){return t.$emit("custom-event",e)}},keyboard_events:{"register-kb-listener":function(e){t.$emit("register-kb-listener",e)},"remove-kb-listener":function(e){t.$emit("remove-kb-listener",e)},keyup:function(e){t.is_active&&t.renderer.propagate("keyup",e)},keydown:function(e){t.is_active&&t.renderer.propagate("keydown",e)},keypress:function(e){t.is_active&&t.renderer.propagate("keypress",e)}}}}},undefined,undefined,!1,null,null,null);le.options.__file="src/components/Grid.vue";var fe,pe=le.exports;function de(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return ge(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ge(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function ge(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var ve=function(){function t(e,n,i){void 0===i&&(i="right"),Y()(this,t),fe=n.config.PANHEIGHT,this.canvas=e,this.ctx=e.getContext("2d"),this.comp=n,this.$p=n.$props,this.data=this.$p.sub,this.range=this.$p.range,this.id=this.$p.grid_id,this.layout=this.$p.layout.grids[this.id],this.side=i,this.listeners()}return U()(t,[{key:"listeners",value:function(){var t=this,e=this.mc=new K.Manager(this.canvas);e.add(new K.Pan({direction:K.DIRECTION_VERTICAL,threshold:0})),e.add(new K.Tap({event:"doubletap",taps:2,posThreshold:50})),e.on("panstart",(function(e){t.$p.y_transform?t.zoom=t.$p.y_transform.zoom:t.zoom=1,t.y_range=[t.layout.$_hi,t.layout.$_lo],t.drug={y:e.center.y,z:t.zoom,mid:_.log_mid(t.y_range,t.layout.height),A:t.layout.A,B:t.layout.B}})),e.on("panmove",(function(e){t.drug&&(t.zoom=t.calc_zoom(e),t.comp.$emit("sidebar-transform",{grid_id:t.id,zoom:t.zoom,auto:!1,range:t.calc_range(),drugging:!0}),t.update())})),e.on("panend",(function(){t.drug=null,t.comp.$emit("sidebar-transform",{grid_id:t.id,drugging:!1})})),e.on("doubletap",(function(){t.comp.$emit("sidebar-transform",{grid_id:t.id,zoom:1,auto:!0}),t.zoom=1,t.update()}))}},{key:"update",value:function(){this.layout=this.$p.layout.grids[this.id];var t,e,n,i,r=this.layout.ys,o=this.side,s=this.layout.sb;switch(this.ctx.fillStyle=this.$p.colors.back,this.ctx.font=this.$p.font,o){case"left":case"right":t=0,e=0,n=Math.floor(s),i=this.layout.height,this.ctx.fillRect(t,e,n,i),this.ctx.strokeStyle=this.$p.colors.scale,this.ctx.beginPath(),this.ctx.moveTo(t+.5,0),this.ctx.lineTo(t+.5,i),this.ctx.stroke()}this.ctx.fillStyle=this.$p.colors.text,this.ctx.beginPath();var a,c=de(r);try{for(c.s();!(a=c.n()).done;){var u=a.value;if(!(u[0]>this.layout.height)){var h="left"===o?n-.5:t-.5,l="left"===o?h-4.5:h+4.5;this.ctx.moveTo(h,u[0]-.5),this.ctx.lineTo(l,u[0]-.5);var f="left"===o?-10:10;this.ctx.textAlign="left"===o?"end":"start";var p=this.layout.prec;this.ctx.fillText(u[1].toFixed(p),h+f,u[0]+4)}}}catch(t){c.e(t)}finally{c.f()}this.ctx.stroke(),this.$p.grid_id&&this.upper_border(),this.apply_shaders(),this.$p.cursor.y&&this.$p.cursor.y$&&this.panel()}},{key:"apply_shaders",value:function(){var t,e={layout:this.$p.layout.grids[this.id],cursor:this.$p.cursor},n=de(this.$p.shaders);try{for(n.s();!(t=n.n()).done;){var i=t.value;this.ctx.save(),i.draw(this.ctx,e),this.ctx.restore()}}catch(t){n.e(t)}finally{n.f()}}},{key:"upper_border",value:function(){this.ctx.strokeStyle=this.$p.colors.scale,this.ctx.beginPath(),this.ctx.moveTo(0,.5),this.ctx.lineTo(this.layout.width,.5),this.ctx.stroke()}},{key:"panel",value:function(){if(this.$p.cursor.grid_id===this.layout.id){var t=this.$p.cursor.y$.toFixed(this.layout.prec);this.ctx.fillStyle=this.$p.colors.panel;var e=this.layout.sb+1,n=this.$p.cursor.y-.5*fe-.5;this.ctx.fillRect(-1,n,e,fe),this.ctx.fillStyle=this.$p.colors.textHL,this.ctx.textAlign="left",this.ctx.fillText(t,7,n+15)}}},{key:"calc_zoom",value:function(t){var e=this.drug.y-t.center.y,n=1+(e>0?3:1)*e/this.layout.height;return y.clamp(this.drug.z*n,.005,100)}},{key:"calc_range",value:function(t,e){var n=this;void 0===t&&(t=1),void 0===e&&(e=1);var i=this.zoom/this.drug.z,r=(1/i-1)/2,o=this.y_range.slice(),s=o[0]-o[1];if(this.layout.grid.logScale){var a=this.layout.height/2,c=a-a*(1/i),u=a+a*(1/i),h=function(t){return _.exp((t-n.drug.B)/n.drug.A)};o.slice();o[0]=h(c),o[1]=h(u)}else o[0]=o[0]+s*r*t,o[1]=o[1]-s*r*e;return o}},{key:"rezoom_range",value:function(t,e,n){this.$p.y_transform&&!this.$p.y_transform.auto&&(this.zoom=1,t<0&&(t/=3.75),t*=.25,this.y_range=[this.layout.$_hi,this.layout.$_lo],this.drug={y:0,z:this.zoom,mid:_.log_mid(this.y_range,this.layout.height),A:this.layout.A,B:this.layout.B},this.zoom=this.calc_zoom({center:{y:t*this.layout.height}}),this.comp.$emit("sidebar-transform",{grid_id:this.id,zoom:this.zoom,auto:!1,range:this.calc_range(e,n),drugging:!0}),this.drug=null,this.comp.$emit("sidebar-transform",{grid_id:this.id,drugging:!1}))}},{key:"destroy",value:function(){this.mc&&this.mc.destroy()}},{key:"mousemove",value:function(){}},{key:"mouseout",value:function(){}},{key:"mouseup",value:function(){}},{key:"mousedown",value:function(){}}]),t}(),Ae=Z({name:"Sidebar",props:["sub","layout","range","interval","cursor","colors","font","width","height","grid_id","rerender","y_transform","tv_id","config","shaders"],mixins:[V],mounted:function(){var t=this.$refs.canvas;this.renderer=new ve(t,this),this.setup(),this.redraw()},render:function(t){var e=this.$props.grid_id,n=this.$props.layout.grids[e];return this.create_canvas(t,"sidebar-".concat(e),{position:{x:n.width,y:n.offset||0},attrs:{rerender:this.$props.rerender,width:this.$props.width,height:n.height},style:{backgroundColor:this.$props.colors.back}})},watch:{range:{handler:function(){this.redraw()},deep:!0},cursor:{handler:function(){this.redraw()},deep:!0},rerender:function(){var t=this;this.$nextTick((function(){return t.redraw()}))}},beforeDestroy:function(){this.renderer&&this.renderer.destroy()}},undefined,undefined,!1,null,null,null);Ae.options.__file="src/components/Sidebar.vue";var me=Ae.exports,ye=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"trading-vue-legend",style:t.calc_style},[0===t.grid_id?n("div",{staticClass:"trading-vue-ohlcv",style:{"max-width":t.common.width+"px"}},[n("span",{staticClass:"t-vue-title",style:{color:t.common.colors.title}},[t._v("\n              "+t._s(t.common.title_txt)+"\n        ")]),t._v(" "),t.show_values?n("span",[t._v("\n            O"),n("span",{staticClass:"t-vue-lspan"},[t._v(t._s(t.ohlcv[0]))]),t._v("\n            H"),n("span",{staticClass:"t-vue-lspan"},[t._v(t._s(t.ohlcv[1]))]),t._v("\n            L"),n("span",{staticClass:"t-vue-lspan"},[t._v(t._s(t.ohlcv[2]))]),t._v("\n            C"),n("span",{staticClass:"t-vue-lspan"},[t._v(t._s(t.ohlcv[3]))]),t._v("\n            V"),n("span",{staticClass:"t-vue-lspan"},[t._v(t._s(t.ohlcv[4]))])]):t._e(),t._v(" "),t.show_values?t._e():n("span",{staticClass:"t-vue-lspan",style:{color:t.common.colors.text}},[t._v("\n            "+t._s((t.common.meta.last||[])[4])+"\n        ")])]):t._e(),t._v(" "),t._l(this.indicators,(function(e){return n("div",{staticClass:"t-vue-ind"},[n("span",{staticClass:"t-vue-iname"},[t._v(t._s(e.name))]),t._v(" "),n("button-group",{attrs:{buttons:t.common.buttons,config:t.common.config,ov_id:e.id,grid_id:t.grid_id,index:e.index,tv_id:t.common.tv_id,display:e.v},on:{"legend-button-click":t.button_click}}),t._v(" "),e.v?n("span",{staticClass:"t-vue-ivalues"},t._l(e.values,(function(e){return t.show_values?n("span",{staticClass:"t-vue-lspan t-vue-ivalue",style:{color:e.color}},[t._v("\n                "+t._s(e.value)+"\n            ")]):t._e()})),0):t._e(),t._v(" "),e.unk?n("span",{staticClass:"t-vue-unknown"},[t._v("\n            (Unknown type)\n        ")]):t._e(),t._v(" "),n("transition",{attrs:{name:"tvjs-appear"}},[e.loading?n("spinner",{attrs:{colors:t.common.colors}}):t._e()],1)],1)}))],2)};ye._withStripped=!0;var _e=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",{staticClass:"t-vue-lbtn-grp"},t._l(t.buttons,(function(e,i){return n("legend-button",{key:i,attrs:{id:e.name||e,tv_id:t.tv_id,ov_id:t.ov_id,grid_id:t.grid_id,index:t.index,display:t.display,icon:e.icon,config:t.config},on:{"legend-button-click":t.button_click}})})),1)};_e._withStripped=!0;var we=function(){var t=this.$createElement;return(this._self._c||t)("img",{staticClass:"t-vue-lbtn",style:{width:this.config.L_BTN_SIZE+"px",height:this.config.L_BTN_SIZE+"px",margin:this.config.L_BTN_MARGIN},attrs:{src:this.base64,id:this.uuid},on:{click:this.onclick}})};we._withStripped=!0;var be={name:"LegendButton",props:["id","tv_id","grid_id","ov_id","index","display","icon","config"],mounted:function(){},computed:{base64:function(){return this.icon||Ht[this.file_name]},file_name:function(){var t=this.$props.id;return"display"===this.$props.id&&(t=this.$props.display?"display_on":"display_off"),t+".png"},uuid:function(){var t=this.$props.tv_id,e=this.$props.grid_id,n=this.$props.ov_id;return"".concat(t,"-btn-g").concat(e,"-").concat(n)},data_type:function(){return 0===this.$props.grid_id?"onchart":"offchart"},data_index:function(){return this.$props.index}},methods:{onclick:function(){this.$emit("legend-button-click",{button:this.$props.id,type:this.data_type,dataIndex:this.data_index,grid:this.$props.grid_id,overlay:this.$props.ov_id})}}},xe=(n(47),Z(be,we,[],!1,null,null,null));xe.options.__file="src/components/LegendButton.vue";var ke={name:"ButtonGroup",props:["buttons","tv_id","ov_id","grid_id","index","display","config"],components:{LegendButton:xe.exports},methods:{button_click:function(t){this.$emit("legend-button-click",t)}}},Ce=(n(49),Z(ke,_e,[],!1,null,null,null));Ce.options.__file="src/components/ButtonGroup.vue";var Ie=Ce.exports,Be=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"tvjs-spinner"},t._l(4,(function(e){return n("div",{key:e,style:{background:t.colors.text}})})),0)};Be._withStripped=!0;var Ee={name:"Spinner",props:["colors"]},Se=(n(51),Z(Ee,Be,[],!1,null,null,null));Se.options.__file="src/components/Spinner.vue";var Te={name:"ChartLegend",props:["common","values","grid_id","meta_props"],components:{ButtonGroup:Ie,Spinner:Se.exports},computed:{ohlcv:function(){if(!this.$props.values||!this.$props.values.ohlcv)return Array(6).fill("n/a");var t=this.layout.prec,e=this.main_type+"_0",n=this.$props.meta_props[e]||{};return n.legend?(n.legend()||[]).map((function(t){return t.value})):[this.$props.values.ohlcv[1].toFixed(t),this.$props.values.ohlcv[2].toFixed(t),this.$props.values.ohlcv[3].toFixed(t),this.$props.values.ohlcv[4].toFixed(t),this.$props.values.ohlcv[5]?this.$props.values.ohlcv[5].toFixed(2):"n/a"]},indicators:function(){var t=this,e=this.$props.values,n=this.format,i={};return this.json_data.filter((function(t){return!1!==t.settings.legend&&!t.main})).map((function(r){r.type in i||(i[r.type]=0);var o=r.type+"_".concat(i[r.type]++);return{v:!("display"in r.settings)||r.settings.display,name:r.name||o,index:(t.off_data||t.json_data).indexOf(r),id:o,values:e?n(o,e):t.n_a(1),unk:!(o in(t.$props.meta_props||{})),loading:r.loading}}))},calc_style:function(){var t=this.layout.height>150?10:5,e=this.$props.common.layout.grids,n=e[0]?e[0].width:void 0;return{top:"".concat(this.layout.offset+t,"px"),width:"".concat(n-20,"px")}},layout:function(){var t=this.$props.grid_id;return this.$props.common.layout.grids[t]},json_data:function(){return this.$props.common.data},off_data:function(){return this.$props.common.offchart},main_type:function(){var t=this.common.data.find((function(t){return t.main}));return t?t.type:void 0},show_values:function(){return"explore"!==this.common.cursor.mode}},methods:{format:function(t,e){var n=this.$props.meta_props[t]||{};return e[t]?n.legend?n.legend(e[t]):e[t].slice(1).map((function(t,e){var i=n.data_colors?n.data_colors():[];return"number"==typeof t&&(t=t.toFixed(Math.abs(t)>.001?4:8)),{value:t,color:i?i[e%i.length]:void 0}})):this.n_a(1)},n_a:function(t){return Array(t).fill({value:"n/a"})},button_click:function(t){this.$emit("legend-button-click",t)}}},Me=(n(53),Z(Te,ye,[],!1,null,null,null));function Oe(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Qe(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Qe(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Qe(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}Me.options.__file="src/components/Legend.vue";var De={methods:{init_shaders:function(t,e){if(t!==e){e&&(this.shaders=this.shaders.filter((function(t){return t.owner!==e.id})));var n,i=Oe(t.shaders);try{for(i.s();!(n=i.n()).done;){var r=new(0,n.value);r.owner=t.id,this.shaders.push(r)}}catch(t){i.e(t)}finally{i.f()}}},on_shader_event:function(t,e){if("new-shader"===t.event&&t.args[0].target===e&&(t.args[0].id="".concat(t.args[1],"-").concat(t.args[2]),this.shaders.push(t.args[0]),this.rerender++),"remove-shaders"===t.event){var n=t.args.join("-");this.shaders=this.shaders.filter((function(t){return t.id!==n}))}}},watch:{skin:function(t,e){this.init_shaders(t,e)}},data:function(){return{shaders:[]}}},Ge={name:"GridSection",props:["common","grid_id"],mixins:[De],components:{Grid:pe,Sidebar:me,ChartLegend:Me.exports},mounted:function(){this.init_shaders(this.$props.common.skin)},methods:{range_changed:function(t){this.$emit("range-changed",t)},cursor_changed:function(t){t.grid_id=this.$props.grid_id,this.$emit("cursor-changed",t)},cursor_locked:function(t){this.$emit("cursor-locked",t)},sidebar_transform:function(t){this.$emit("sidebar-transform",t)},emit_meta_props:function(t){this.$set(this.meta_props,t.layer_id,t),this.$emit("layer-meta-props",t)},emit_custom_event:function(t){this.on_shader_event(t,"sidebar"),this.$emit("custom-event",t)},button_click:function(t){this.$emit("legend-button-click",t)},register_kb:function(t){this.$emit("register-kb-listener",t)},remove_kb:function(t){this.$emit("remove-kb-listener",t)},rezoom_range:function(t){var e="sb-"+t.grid_id;this.$refs[e]&&this.$refs[e].renderer.rezoom_range(t.z,t.diff1,t.diff2)},ghash:function(t){return t.layout.grids.map((function(t){return t.height})).reduce((function(t,e){return t+e}),"")}},computed:{grid_props:function(){var t=this.$props.grid_id,e=Object.assign({},this.$props.common);if(t>0){var n,i=e.data;e.data=[e.data[t-1]],(n=e.data).push.apply(n,o()(i.filter((function(e){return e.grid&&e.grid.id===t}))))}return e.width=e.layout.grids[t].width,e.height=e.layout.grids[t].height,e.y_transform=e.y_ts[t],e.shaders=this.grid_shaders,e},sidebar_props:function(){var t=this.$props.grid_id,e=Object.assign({},this.$props.common);return e.width=e.layout.grids[t].sb,e.height=e.layout.grids[t].height,e.y_transform=e.y_ts[t],e.shaders=this.sb_shaders,e},section_values:function(){var t=this.$props.grid_id,e=Object.assign({},this.$props.common);return e.width=e.layout.grids[t].width,e.cursor.values[t]},legend_props:function(){var t=this.$props.grid_id,e=Object.assign({},this.$props.common);if(t>0){var n,i=e.data;e.offchart=i,e.data=[e.data[t-1]],(n=e.data).push.apply(n,o()(i.filter((function(e){return e.grid&&e.grid.id===t}))))}return e},get_meta_props:function(){return this.meta_props},grid_shaders:function(){return this.shaders.filter((function(t){return"grid"===t.target}))},sb_shaders:function(){return this.shaders.filter((function(t){return"sidebar"===t.target}))}},watch:{common:{handler:function(t,e){var n=this.ghash(t);n!==this.last_ghash&&this.rerender++,t.data.length!==e.data.length&&this.rerender++,this.last_ghash=n},deep:!0}},data:function(){return{meta_props:{},rerender:0,last_ghash:""}}},Ye=(n(55),Z(Ge,N,[],!1,null,null,null));Ye.options.__file="src/components/Section.vue";var je=Ye.exports;function Ue(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Re(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Re(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Re(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var Fe=l.MINUTE15,Le=(l.MINUTE,l.HOUR),Ne=l.DAY,Pe=l.WEEK,Ke=l.MONTH,He=l.YEAR,ze=l.MONTHMAP,We=function(){function t(e,n){Y()(this,t),this.canvas=e,this.ctx=e.getContext("2d"),this.comp=n,this.$p=n.$props,this.data=this.$p.sub,this.range=this.$p.range,this.layout=this.$p.layout}return U()(t,[{key:"update",value:function(){this.grid_0=this.layout.grids[0];var t=this.layout.botbar.width,e=this.layout.botbar.height,n=this.layout.grids[0].sb;this.ctx.fillStyle=this.$p.colors.back,this.ctx.font=this.$p.font,this.ctx.fillRect(0,0,t,e),this.ctx.strokeStyle=this.$p.colors.scale,this.ctx.beginPath(),this.ctx.moveTo(0,.5),this.ctx.lineTo(Math.floor(t+1),.5),this.ctx.stroke(),this.ctx.fillStyle=this.$p.colors.text,this.ctx.beginPath();var i,r=Ue(this.layout.botbar.xs);try{for(r.s();!(i=r.n()).done;){var o=i.value,s=this.format_date(o);o[0]>t-n||(this.ctx.moveTo(o[0]-.5,0),this.ctx.lineTo(o[0]-.5,4.5),this.lbl_highlight(o[1][0])||(this.ctx.globalAlpha=.85),this.ctx.textAlign="center",this.ctx.fillText(s,o[0],18),this.ctx.globalAlpha=1)}}catch(t){r.e(t)}finally{r.f()}this.ctx.stroke(),this.apply_shaders(),this.$p.cursor.x&&void 0!==this.$p.cursor.t&&this.panel()}},{key:"apply_shaders",value:function(){var t,e={layout:this.layout.grids[0],cursor:this.$p.cursor},n=Ue(this.comp.bot_shaders);try{for(n.s();!(t=n.n()).done;){var i=t.value;this.ctx.save(),i.draw(this.ctx,e),this.ctx.restore()}}catch(t){n.e(t)}finally{n.f()}}},{key:"panel",value:function(){var t=this.format_cursor_x();this.ctx.fillStyle=this.$p.colors.panel;var e=this.ctx.measureText(t+"    "),n=Math.floor(e.width),i=this.$p.cursor.x,r=Math.floor(i-.5*n),o=this.comp.config.PANHEIGHT;this.ctx.fillRect(r,-.5,n,o+.5),this.ctx.fillStyle=this.$p.colors.textHL,this.ctx.textAlign="center",this.ctx.fillText(t,i,15.5)}},{key:"format_date",value:function(t){var e=t[1][0],n=(e=this.grid_0.ti_map.i2t(e))+(this.$p.layout.grids[0].ti_map.tf<Ne?1:0)*this.$p.timezone*Le,i=new Date(n);return t[2]===He||y.year_start(e)===e?i.getUTCFullYear():t[2]===Ke||y.month_start(e)===e?ze[i.getUTCMonth()]:y.day_start(n)===n?i.getUTCDate():y.add_zero(i.getUTCHours())+":"+y.add_zero(i.getUTCMinutes())}},{key:"format_cursor_x",value:function(){var t=this.$p.cursor.t;t=this.grid_0.ti_map.i2t(t);var e=this.$p.layout.grids[0].ti_map.tf,n=new Date(t+(e<Ne?1:0)*this.$p.timezone*Le);if(e===He)return n.getUTCFullYear();if(e<He)var i="`"+"".concat(n.getUTCFullYear()).slice(-2),r=ze[n.getUTCMonth()],o="01";e<=Pe&&(o=n.getUTCDate());var s="".concat(o," ").concat(r," ").concat(i),a="";e<Ne&&(a=y.add_zero(n.getUTCHours())+":"+y.add_zero(n.getUTCMinutes()));return"".concat(s,"  ").concat(a)}},{key:"lbl_highlight",value:function(t){var e=this.$p.interval;return 0===t||(y.month_start(t)===t||(y.day_start(t)===t||e<=Fe&&t%Le==0))}},{key:"mousemove",value:function(){}},{key:"mouseout",value:function(){}},{key:"mouseup",value:function(){}},{key:"mousedown",value:function(){}}]),t}(),Je={name:"Botbar",props:["sub","layout","range","interval","cursor","colors","font","width","height","rerender","tv_id","config","shaders","timezone"],mixins:[V],mounted:function(){var t=this.$refs.canvas;this.renderer=new We(t,this),this.setup(),this.redraw()},render:function(t){var e=this.$props.layout.botbar;return this.create_canvas(t,"botbar",{position:{x:0,y:e.offset||0},attrs:{rerender:this.$props.rerender,width:e.width,height:e.height},style:{backgroundColor:this.$props.colors.back}})},computed:{bot_shaders:function(){return this.$props.shaders.filter((function(t){return"botbar"===t.target}))}},watch:{range:{handler:function(){this.redraw()},deep:!0},cursor:{handler:function(){this.redraw()},deep:!0},rerender:function(){var t=this;this.$nextTick((function(){return t.redraw()}))}}},$e=(n(57),Z(Je,undefined,undefined,!1,null,null,null));$e.options.__file="src/components/Botbar.vue";var Ve=$e.exports,Xe=Z({name:"Keyboard",created:function(){window.addEventListener("keydown",this.keydown),window.addEventListener("keyup",this.keyup),window.addEventListener("keypress",this.keypress),this._listeners={}},beforeDestroy:function(){window.removeEventListener("keydown",this.keydown),window.removeEventListener("keyup",this.keyup),window.removeEventListener("keypress",this.keypress)},render:function(t){return t()},methods:{keydown:function(t){for(var e in this._listeners){var n=this._listeners[e];n&&n.keydown?n.keydown(t):console.warn("No 'keydown' listener for ".concat(e))}},keyup:function(t){for(var e in this._listeners){var n=this._listeners[e];n&&n.keyup?n.keyup(t):console.warn("No 'keyup' listener for ".concat(e))}},keypress:function(t){for(var e in this._listeners){var n=this._listeners[e];n&&n.keypress?n.keypress(t):console.warn("No 'keypress' listener for ".concat(e))}},register:function(t){this._listeners[t.id]=t},remove:function(t){delete this._listeners[t.id]}}},undefined,undefined,!1,null,null,null);Xe.options.__file="src/components/Keyboard.vue";var Ze=Xe.exports,qe={methods:{data_changed:function(){var t=this.ohlcv,e=!1;return this._data_n0!==t[0]&&this._data_len!==t.length&&(e=!0),this.check_all_data(e),this.ti_map.ib&&this.reindex_delta(t[0],this._data_n0),this._data_n0=t[0],this._data_len=t.length,this.save_data_t(),e},check_all_data:function(t){var e=this._data_len||0;(Math.abs(this.ohlcv.length-e)>1||this._data_n0!==this.ohlcv[0])&&this.$emit("custom-event",{event:"data-len-changed",args:[]})},reindex_delta:function(t,e){if(e=e||[[0]],0!==(t=t||[[0]])[0]-e[0]&&this._data_t)try{var n=this._data_t+.01,i=y.fast_nearest(this.ohlcv,n),r=(n-this.ohlcv[i[0]][0])/this.interval_ms;this.goto(i[0]+r)}catch(t){this.goto(this.ti_map.t2i(this._data_t))}},save_data_t:function(){this._data_t=this.ti_map.i2t(this.range[1])}},data:function(){return{_data_n0:null,_data_len:0,_data_t:0}}},tn=Math.pow(2,32),en=function(){function t(){Y()(this,t),this.ib=!1}return U()(t,[{key:"init",value:function(t,e){t.sub,t.interval;var n=t.meta,i=(t.$props,t.interval_ms),r=t.sub_start,o=t.ib;this.ti_map=[],this.it_map=[],this.sub_i=[],this.ib=o,this.sub=e,this.ss=r,this.tf=i;n.sub_start;this.ib&&this.map_sub(e)}},{key:"map_sub",value:function(t){for(var e=0;e<t.length;e++){var n=t[e][0],i=this.ss+e;this.ti_map[n]=i,this.it_map[i]=n;var r=o()(t[e]);r[0]=i,this.sub_i.push(r)}}},{key:"parse",value:function(t,e){if(!this.ib||!this.sub[0]||"data"===e)return t;var n=[],i=0;if("calc"===e){for(var r=y.index_shift(this.sub,t),s=0;s<t.length;s++){var a=this.ss+s,c=o()(t[s]);c[0]=a+r,n.push(c)}return n}if(t.length)try{var u=y.fast_nearest(this.sub,t[0][0])[0];null!==u&&u>=0&&(i=u)}catch(t){}var h=this.sub[0][0],l=this.sub[this.sub.length-1][0];for(s=0;s<t.length;s++){var f=o()(t[s]),p=this.sub[i][0],d=t[s][0],g=this.ti_map[d];if(void 0===g)if(d<h||d>l)g=this.ss+i-(p-d)/this.tf,d=t[s+1]?t[s+1][0]:void 0;else{var v=this.sub[i+1][0];g=p===v?this.ss+i:this.ss+i+(d-p)/(v-p),d=t[s+1]?t[s+1][0]:void 0}for(;i+1<this.sub.length-1&&d>this.sub[i+1][0];)i++,p=this.sub[i][0];f[0]=g,n.push(f)}return n}},{key:"i2t",value:function(t){if(!this.ib||!this.sub.length)return t;var e=this.it_map[t];if(void 0!==e)return e;if(t>=this.ss+this.sub_i.length){var n=t-(this.ss+this.sub_i.length)+1;return this.sub[this.sub.length-1][0]+n*this.tf}if(t<this.ss){var i=t-this.ss;return this.sub[0][0]+i*this.tf}var r=Math.floor(t)-this.ss,o=r+1,s=this.sub.length;o>=s&&(o=s-1);var a=this.sub[r],c=this.sub[o];if(a&&c){var u=a[0];return u+(c[0]-u)*(t-r-this.ss)}}},{key:"i2t_mode",value:function(t,e){return"data"===e?t:this.i2t(t)}},{key:"t2i",value:function(t){if(this.sub.length){var e=this.ti_map[t];if(void 0!==e)return e;var n=this.sub[0][0],i=this.sub[this.sub.length-1][0];if(t<n)return this.ss-(n-t)/this.tf;if(t>i){var r=this.sub.length-1;return this.ss+r-(i-t)/this.tf}try{var o=y.fast_nearest(this.sub,t),s=this.sub[o[0]][0],a=(t-s)/(this.sub[o[1]][0]-s);return this.ss+o[0]+a*(o[1]-o[0])}catch(t){}}}},{key:"smth2i",value:function(t){return t>tn?this.t2i(t):t}},{key:"smth2t",value:function(t){return t<tn?this.i2t(t):t}}]),t}(),nn=Z({name:"Chart",props:["title_txt","data","width","height","font","colors","overlays","tv_id","config","buttons","toolbar","ib","skin","timezone"],mixins:[De,qe],components:{GridSection:je,Botbar:Ve,Keyboard:Ze},created:function(){this.ctx=new v(this.$props),this.init_range(),this.sub=this.subset(),y.overwrite(this.range,this.range),this._layout=new D(this),this.updater=new L(this),this.update_last_candle(),this.init_shaders(this.skin)},methods:{range_changed:function(t){var e=this.subset(t);y.overwrite(this.range,t),y.overwrite(this.sub,e),this.update_layout(),this.$emit("range-changed",t),this.$props.ib&&this.save_data_t()},goto:function(t){var e=this.range[1]-this.range[0];this.range_changed([t-e,t])},setRange:function(t,e){this.range_changed([t,e])},cursor_changed:function(t){t.mode&&(this.cursor.mode=t.mode),"explore"!==this.cursor.mode&&this.updater.sync(t),this._hook_xchanged&&this.ce("?x-changed",t)},cursor_locked:function(t){this.cursor.scroll_lock&&t||(this.cursor.locked=t,this._hook_xlocked&&this.ce("?x-locked",t))},calc_interval:function(){var t=this,e=y.parse_tf(this.forced_tf);this.ohlcv.length<2&&!e||(this.interval_ms=e||y.detect_interval(this.ohlcv),this.interval=this.$props.ib?1:this.interval_ms,y.warn((function(){return t.$props.ib&&!t.chart.tf}),l.IB_TF_WARN,l.SECOND))},set_ytransform:function(t){var e=this.y_transforms[t.grid_id]||{};Object.assign(e,t),this.$set(this.y_transforms,t.grid_id,e),this.update_layout(),y.overwrite(this.range,this.range)},default_range:function(){var t=this.$props.config.DEFAULT_LEN,e=this.$props.config.MINIMUM_LEN+.5,n=this.ohlcv.length-1;if(!(this.ohlcv.length<2)){if(this.ohlcv.length<=t)var i=0,r=e;else i=n-t,r=.5;this.$props.ib?y.overwrite(this.range,[i-this.interval*r,n+this.interval*e]):y.overwrite(this.range,[this.ohlcv[i][0]-this.interval*r,this.ohlcv[n][0]+this.interval*e])}},subset:function(t){void 0===t&&(t=this.range);var e=this.filter(this.ohlcv,t[0]-this.interval,t[1]),n=d()(e,2),i=n[0],r=n[1];return this.ti_map=new en,i?(this.sub_start=r,this.ti_map.init(this,i),this.$props.ib?this.ti_map.sub_i:i||[]):[]},common_props:function(){return{title_txt:this.chart.name||this.$props.title_txt,layout:this._layout,sub:this.sub,range:this.range,interval:this.interval,cursor:this.cursor,colors:this.$props.colors,font:this.$props.font,y_ts:this.y_transforms,tv_id:this.$props.tv_id,config:this.$props.config,buttons:this.$props.buttons,meta:this.meta,skin:this.$props.skin}},overlay_subset:function(t){var e=this;return t.map((function(t){var n=y.fast_filter(t.data,e.ti_map.i2t_mode(e.range[0]-e.interval,t.indexSrc),e.ti_map.i2t_mode(e.range[1],t.indexSrc));return{type:t.type,name:y.format_name(t),data:e.ti_map.parse(n[0]||[],t.indexSrc||"map"),settings:t.settings||e.settings_ov,grid:t.grid||{},tf:y.parse_tf(t.tf),i0:n[1],loading:t.loading}}))},section_props:function(t){return 0===t?this.main_section:this.sub_section},init_range:function(){this.calc_interval(),this.default_range()},layer_meta_props:function(t){t.grid_id in this.layers_meta||this.$set(this.layers_meta,t.grid_id,{}),this.$set(this.layers_meta[t.grid_id],t.layer_id,t),this.update_layout()},remove_meta_props:function(t,e){t in this.layers_meta&&this.$delete(this.layers_meta[t],e)},emit_custom_event:function(t){this.on_shader_event(t,"botbar"),this.$emit("custom-event",t),"remove-layer-meta"===t.event&&this.remove_meta_props.apply(this,o()(t.args))},update_layout:function(t){t&&this.calc_interval();var e=new D(this);y.copy_layout(this._layout,e),this._hook_update&&this.ce("?chart-update",e)},legend_button_click:function(t){this.$emit("legend-button-click",t)},register_kb:function(t){this.$refs.keyboard&&this.$refs.keyboard.register(t)},remove_kb:function(t){this.$refs.keyboard&&this.$refs.keyboard.remove(t)},update_last_candle:function(){this.last_candle=this.ohlcv?this.ohlcv[this.ohlcv.length-1]:void 0},ce:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];this.emit_custom_event({event:t,args:n})},hooks:function(){for(var t=this,e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];n.forEach((function(e){return t["_hook_".concat(e)]=!0}))}},computed:{main_section:function(){var t=Object.assign({},this.common_props());return t.data=this.overlay_subset(this.onchart),t.data.push({type:this.chart.type||"Candles",main:!0,data:this.sub,i0:this.sub_start,settings:this.chart.settings||this.settings_ohlcv,grid:this.chart.grid||{}}),t.overlays=this.$props.overlays,t},sub_section:function(){var t=Object.assign({},this.common_props());return t.data=this.overlay_subset(this.offchart),t.overlays=this.$props.overlays,t},botbar_props:function(){var t=Object.assign({},this.common_props());return t.width=t.layout.botbar.width,t.height=t.layout.botbar.height,t.rerender=this.rerender,t},offsub:function(){return this.overlay_subset(this.offchart)},ohlcv:function(){return this.$props.data.ohlcv||this.chart.data||[]},chart:function(){return this.$props.data.chart||{grid:{}}},onchart:function(){return this.$props.data.onchart||[]},offchart:function(){return this.$props.data.offchart||[]},filter:function(){return this.$props.ib?y.fast_filter_i:y.fast_filter},styles:function(){var t=this.$props.toolbar?this.$props.config.TOOLBAR:0;return{"margin-left":"".concat(t,"px")}},meta:function(){return{last:this.last_candle,sub_start:this.sub_start,activated:this.activated}},forced_tf:function(){return this.chart.tf}},data:function(){return{sub:[],range:[],interval:0,cursor:{x:null,y:null,t:null,y$:null,grid_id:null,locked:!1,values:{},scroll_lock:!1,mode:y.xmode()},rerender:0,layers_meta:{},y_transforms:{},settings_ohlcv:{},settings_ov:{},last_candle:[],sub_start:void 0,activated:!1}},watch:{width:function(){this.update_layout(),this._hook_resize&&this.ce("?chart-resize")},height:function(){this.update_layout(),this._hook_resize&&this.ce("?chart-resize")},ib:function(t){if(t)this.init_range(),y.overwrite(this.range,this.range),this.interval=1;else{var e=this.ti_map.i2t(this.range[0]),n=this.ti_map.i2t(this.range[1]);y.overwrite(this.range,[e,n]),this.interval=this.interval_ms}var i=this.subset();y.overwrite(this.sub,i),this.update_layout()},timezone:function(){this.update_layout()},colors:function(){y.overwrite(this.range,this.range)},forced_tf:function(t,e){this.update_layout(!0),this.ce("exec-all-scripts")},data:{handler:function(t,e){this.sub.length||this.init_range();var n=this.subset();(this.sub.length||n.length)&&y.overwrite(this.sub,n);var i=this.data_changed();this.update_layout(i),y.overwrite(this.range,this.range),this.cursor.scroll_lock=!!t.scrollLock,t.scrollLock&&this.cursor.locked&&(this.cursor.locked=!1),this._hook_data&&this.ce("?chart-data",i),this.update_last_candle(),this.rerender++},deep:!0}}},f,[],!1,null,null,null);nn.options.__file="src/components/Chart.vue";var rn=nn.exports,on=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{key:t.tool_count,staticClass:"trading-vue-toolbar",style:t.styles},t._l(t.groups,(function(e,i){return e.icon&&!e.hidden?n("toolbar-item",{key:i,attrs:{data:e,subs:t.sub_map,dc:t.data,config:t.config,colors:t.colors,selected:t.is_selected(e)},on:{"item-selected":t.selected}}):t._e()})),1)};on._withStripped=!0;var sn=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{class:["trading-vue-tbitem",t.selected?"selected-item":""],style:t.item_style,on:{click:function(e){return t.emit_selected("click")},mousedown:t.mousedown,touchstart:t.mousedown,touchend:function(e){return t.emit_selected("touch")}}},[n("div",{staticClass:"trading-vue-tbicon tvjs-pixelated",style:t.icon_style}),t._v(" "),t.data.group?n("div",{staticClass:"trading-vue-tbitem-exp",style:t.exp_style,on:{click:t.exp_click,mousedown:t.expmousedown,mouseover:t.expmouseover,mouseleave:t.expmouseleave}},[t._v("\n        ᐳ\n    ")]):t._e(),t._v(" "),t.show_exp_list?n("item-list",{attrs:{config:t.config,items:t.data.items,colors:t.colors,dc:t.dc},on:{"close-list":t.close_list,"item-selected":t.emit_selected_sub}}):t._e()],1)};sn._withStripped=!0;var an=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"tvjs-item-list",style:t.list_style(),on:{mousedown:t.thismousedown}},t._l(t.items,(function(e){return e.hidden?t._e():n("div",{class:t.item_class(e),style:t.item_style(e),on:{click:function(n){return t.item_click(n,e)}}},[n("div",{staticClass:"trading-vue-tbicon tvjs-pixelated",style:t.icon_style(e)}),t._v(" "),n("div",[t._v(t._s(e.type))])])})),0)};an._withStripped=!0;var cn={name:"ItemList",props:["config","items","colors","dc"],mounted:function(){window.addEventListener("mousedown",this.onmousedown)},beforeDestroy:function(){window.removeEventListener("mousedown",this.onmousedown)},methods:{list_style:function(){var t=this.$props.config.TOOLBAR,e=this.colors.tbListBorder||this.colors.grid,n="1px solid ".concat(e);return{left:"".concat(t,"px"),background:this.colors.back,borderTop:n,borderRight:n,borderBottom:n}},item_class:function(t){return this.dc.tool===t.type?"tvjs-item-list-item selected-item":"tvjs-item-list-item"},item_style:function(t){var e=this.$props.config,n=e.TB_ICON+2*e.TB_ITEM_M+8,i=this.dc.tool===t.type;return{height:"".concat(n,"px"),color:i?void 0:"#888888"}},icon_style:function(t){var e=this.$props.config,n=e.TB_ICON_BRI,i=e.TB_ITEM_M;return{"background-image":"url(".concat(t.icon,")"),width:"25px",height:"25px",margin:"".concat(i,"px"),filter:"brightness(".concat(n,")")}},item_click:function(t,e){t.cancelBubble=!0,this.$emit("item-selected",e),this.$emit("close-list")},onmousedown:function(){this.$emit("close-list")},thismousedown:function(t){t.stopPropagation()}},computed:{},data:function(){return{}}},un=(n(59),Z(cn,an,[],!1,null,null,null));un.options.__file="src/components/ItemList.vue";var hn={name:"ToolbarItem",props:["data","selected","colors","tv_id","config","dc","subs"],components:{ItemList:un.exports},mounted:function(){if(this.data.group){var t=this.subs[this.data.group],e=this.data.items.find((function(e){return e.type===t}));e&&(this.sub_item=e)}},methods:{mousedown:function(t){var e=this;this.click_start=y.now(),this.click_id=setTimeout((function(){e.show_exp_list=!0}),this.config.TB_ICON_HOLD)},expmouseover:function(){this.exp_hover=!0},expmouseleave:function(){this.exp_hover=!1},expmousedown:function(t){this.show_exp_list&&t.stopPropagation()},emit_selected:function(t){if(!(y.now()-this.click_start>this.config.TB_ICON_HOLD))if(clearTimeout(this.click_id),this.data.group){var e=this.sub_item||this.data.items[0];this.$emit("item-selected",e)}else this.$emit("item-selected",this.data)},emit_selected_sub:function(t){this.$emit("item-selected",t),this.sub_item=t},exp_click:function(t){this.data.group&&(t.cancelBubble=!0,this.show_exp_list=!this.show_exp_list)},close_list:function(){this.show_exp_list=!1}},computed:{item_style:function(){if("System:Splitter"===this.$props.data.type)return this.splitter;var t=this.$props.config,e=t.TB_ITEM_M,n=.5*(t.TOOLBAR-t.TB_ICON)-e,i=t.TB_ICON+2*e,r=this.exp_hover?0:3;return{width:"".concat(i,"px"),height:"".concat(i,"px"),margin:"8px ".concat(n,"px 0px ").concat(n,"px"),"border-radius":"3px ".concat(r,"px ").concat(r,"px 3px")}},icon_style:function(){if("System:Splitter"===this.$props.data.type)return{};var t=this.$props.config,e=t.TB_ICON_BRI,n=t.TB_ICON,i=t.TB_ITEM_M,r=this.sub_item?this.sub_item.icon:this.$props.data.icon;return{"background-image":"url(".concat(r,")"),width:"".concat(n,"px"),height:"".concat(n,"px"),margin:"".concat(i,"px"),filter:"brightness(".concat(e,")")}},exp_style:function(){var t=this.$props.config,e=t.TB_ITEM_M,n=.5*t.TB_ICON+e,i=(t.TOOLBAR-2*n)/4;return{padding:"".concat(n,"px ").concat(i,"px"),transform:this.show_exp_list?"scale(-0.6, 1)":"scaleX(0.6)"}},splitter:function(){var t=this.$props.config,e=this.$props.colors.grid,n=t.TB_ITEM_M,i=.5*(t.TOOLBAR-t.TB_ICON)-n,r=t.TB_ICON+2*n;return{width:"".concat(r,"px"),height:"1px",margin:"8px ".concat(i,"px 8px ").concat(i,"px"),"background-color":e}}},data:function(){return{exp_hover:!1,show_exp_list:!1,sub_item:null}}},ln=(n(61),Z(hn,sn,[],!1,null,null,null));function fn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return pn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pn(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function pn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}ln.options.__file="src/components/ToolbarItem.vue";var dn={name:"Toolbar",props:["data","height","colors","tv_id","config"],components:{ToolbarItem:ln.exports},mounted:function(){},methods:{selected:function(t){this.$emit("custom-event",{event:"tool-selected",args:[t.type]}),t.group&&(this.sub_map[t.group]=t.type)},is_selected:function(t){var e=this;return t.group?!!t.items.find((function(t){return t.type===e.data.tool})):t.type===this.data.tool}},computed:{styles:function(){var t=this.$props.colors,e=this.$props.config.TB_BORDER,n=this.$props.config.TOOLBAR-e,i=(t.grid,t.tbBack||t.back),r=t.tbBorder||t.scale,o=this.$props.config.TB_B_STYLE;return{width:"".concat(n,"px"),height:"".concat(this.$props.height-3,"px"),"background-color":i,"border-right":"".concat(e,"px ").concat(o," ").concat(r)}},groups:function(){var t,e=[],n=fn(this.data.tools||[]);try{for(n.s();!(t=n.n()).done;){var i=t.value;if(i.group){var r=e.find((function(t){return t.group===i.group}));r?r.items.push(i):e.push({group:i.group,icon:i.icon,items:[i]})}else e.push(i)}}catch(t){n.e(t)}finally{n.f()}return e}},watch:{data:{handler:function(t){t.tools&&(this.tool_count=t.tools.length)},deep:!0}},data:function(){return{tool_count:0,sub_map:{}}}},gn=(n(63),Z(dn,on,[],!1,null,null,null));gn.options.__file="src/components/Toolbar.vue";var vn=gn.exports,An=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"tvjs-widgets",style:{width:t.width+"px",height:t.height+"px"}},t._l(Object.keys(t.map),(function(e){return n(t.initw(e),{key:e,tag:"component",attrs:{id:e,main:t.map[e].ctrl,data:t.map[e].data,tv:t.tv,dc:t.dc}})})),1)};An._withStripped=!0;var mn={name:"Widgets",props:["width","height","map","tv","dc"],methods:{initw:function(t){return this.$props.map[t].cls}}},yn=(n(65),Z(mn,An,[],!1,null,null,null));yn.options.__file="src/components/Widgets.vue";var _n=yn.exports,wn=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div",{staticClass:"tvjs-the-tip",style:t.style,domProps:{innerHTML:t._s(t.data.text)},on:{mousedown:function(e){return t.$emit("remove-me")}}})};wn._withStripped=!0;var bn={name:"TheTip",props:["data"],mounted:function(){var t=this;setTimeout((function(){return t.$emit("remove-me")}),3e3)},computed:{style:function(){return{background:this.data.color}}}},xn=(n(67),Z(bn,wn,[],!1,null,null,null));function kn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Cn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Cn(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Cn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function In(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Bn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Bn(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Bn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}xn.options.__file="src/components/TheTip.vue";var En={name:"TradingVue",components:{Chart:rn,Toolbar:vn,Widgets:_n,TheTip:xn.exports},mixins:[{mounted:function(){this.ctrllist(),this.skin_styles()},methods:{ctrllist:function(){this.ctrl_destroy(),this.controllers=[];var t,e=kn(this.$props.extensions);try{for(e.s();!(t=e.n()).done;){var n=t.value,i=n.Main.__name__;this.xSettings[i]||this.$set(this.xSettings,i,{});var r=new n.Main(this,this.data,this.xSettings[i]);r.name=i,this.controllers.push(r)}}catch(t){e.e(t)}finally{e.f()}return this.controllers},pre_dc:function(t){var e,n=kn(this.controllers);try{for(n.s();!(e=n.n()).done;){var i=e.value;i.update&&i.update(t)}}catch(t){n.e(t)}finally{n.f()}},post_dc:function(t){var e,n=kn(this.controllers);try{for(n.s();!(e=n.n()).done;){var i=e.value;i.post_update&&i.post_update(t)}}catch(t){n.e(t)}finally{n.f()}},ctrl_destroy:function(){var t,e=kn(this.controllers);try{for(e.s();!(t=e.n()).done;){var n=t.value;n.destroy&&n.destroy()}}catch(t){e.e(t)}finally{e.f()}},skin_styles:function(){var t="tvjs-skin-styles",e=document.getElementById(t);e&&e.parentNode.removeChild(e);if(this.skin_proto&&this.skin_proto.styles){var n=document.createElement("style");n.setAttribute("id",t),n.innerHTML=this.skin_proto.styles,this.$el.appendChild(n)}}},computed:{ws:function(){var t,e={},n=kn(this.controllers);try{for(n.s();!(t=n.n()).done;){var i=t.value;if(i.widgets)for(var r in i.widgets)e[r]=i.widgets[r],e[r].ctrl=i}}catch(t){n.e(t)}finally{n.f()}return e},skins:function(){var t,e={},n=kn(this.$props.extensions);try{for(n.s();!(t=n.n()).done;){var i=t.value;for(var r in i.skins||{})e[r]=i.skins[r]}}catch(t){n.e(t)}finally{n.f()}return e},skin_proto:function(){return this.skins[this.$props.skin]},colorpack:function(){var t=this.skins[this.$props.skin];return t?t.colors:void 0}},watch:{skin:function(t,e){t!==e&&this.resetChart(),this.skin_styles()},extensions:function(){this.ctrllist()},xSettings:{handler:function(t,e){var n,i=kn(this.controllers);try{for(i.s();!(n=i.n()).done;){var r=n.value;r.onsettings&&r.onsettings(t,e)}}catch(t){i.e(t)}finally{i.f()}},deep:!0}},data:function(){return{controllers:[]}}}],props:{titleTxt:{type:String,default:"TradingVue.js"},id:{type:String,default:"trading-vue-js"},width:{type:Number,default:800},height:{type:Number,default:421},colorTitle:{type:String,default:"#42b883"},colorBack:{type:String,default:"#121826"},colorGrid:{type:String,default:"#2f3240"},colorText:{type:String,default:"#dedddd"},colorTextHL:{type:String,default:"#fff"},colorScale:{type:String,default:"#838383"},colorCross:{type:String,default:"#8091a0"},colorCandleUp:{type:String,default:"#23a776"},colorCandleDw:{type:String,default:"#e54150"},colorWickUp:{type:String,default:"#23a77688"},colorWickDw:{type:String,default:"#e5415088"},colorWickSm:{type:String,default:"transparent"},colorVolUp:{type:String,default:"#79999e42"},colorVolDw:{type:String,default:"#ef535042"},colorPanel:{type:String,default:"#565c68"},colorTbBack:{type:String},colorTbBorder:{type:String,default:"#8282827d"},colors:{type:Object},font:{type:String,default:l.ChartConfig.FONT},toolbar:{type:Boolean,default:!1},data:{type:Object,required:!0},overlays:{type:Array,default:function(){return[]}},chartConfig:{type:Object,default:function(){return{}}},legendButtons:{type:Array,default:function(){return[]}},indexBased:{type:Boolean,default:!1},extensions:{type:Array,default:function(){return[]}},xSettings:{type:Object,default:function(){return{}}},skin:{type:String},timezone:{type:Number,default:0}},computed:{chart_props:function(){var t=this.$props.toolbar?this.chart_config.TOOLBAR:0,e={title_txt:this.$props.titleTxt,overlays:this.$props.overlays.concat(this.mod_ovs),data:this.decubed,width:this.$props.width-t,height:this.$props.height,font:this.font_comp,buttons:this.$props.legendButtons,toolbar:this.$props.toolbar,ib:this.$props.indexBased||this.index_based||!1,colors:Object.assign({},this.$props.colors||this.colorpack),skin:this.skin_proto,timezone:this.$props.timezone};return this.parse_colors(e.colors),e},chart_config:function(){return Object.assign({},l.ChartConfig,this.$props.chartConfig)},decubed:function(){var t=this.$props.data;return void 0!==t.data?(t.init_tvjs(this),t.data):t},index_based:function(){var t=this.$props.data;return t.chart?t.chart.indexBased:!!t.data&&t.data.chart.indexBased},mod_ovs:function(){var t,e=[],n=In(this.$props.extensions);try{for(n.s();!(t=n.n()).done;){var i=t.value;e.push.apply(e,o()(Object.values(i.overlays)))}}catch(t){n.e(t)}finally{n.f()}return e},font_comp:function(){return this.skin_proto&&this.skin_proto.font?this.skin_proto.font:this.font}},data:function(){return{reset:0,tip:null}},beforeDestroy:function(){this.custom_event({event:"before-destroy"}),this.ctrl_destroy()},methods:{resetChart:function(t){var e=this;void 0===t&&(t=!0),this.reset++;var n=this.getRange();!t&&n[0]&&n[1]&&this.$nextTick((function(){return e.setRange.apply(e,o()(n))})),this.$nextTick((function(){return e.custom_event({event:"chart-reset",args:[]})}))},goto:function(t){this.chart_props.ib&&(t=this.$refs.chart.ti_map.smth2i(t));this.$refs.chart.goto(t)},setRange:function(t,e){if(this.chart_props.ib){var n=this.$refs.chart.ti_map;t=n.smth2i(t),e=n.smth2i(e)}this.$refs.chart.setRange(t,e)},getRange:function(){if(this.chart_props.ib){var t=this.$refs.chart.ti_map;return this.$refs.chart.range.map((function(e){return t.i2t(e)}))}return this.$refs.chart.range},getCursor:function(){var t=this.$refs.chart.cursor;if(this.chart_props.ib){var e=this.$refs.chart.ti_map,n=Object.assign({},t);return n.i=n.t,n.t=e.i2t(n.t),n}return t},showTheTip:function(t,e){void 0===e&&(e="orange"),this.tip={text:t,color:e}},legend_button:function(t){this.custom_event({event:"legend-button-click",args:[t]})},custom_event:function(t){"args"in t?this.$emit.apply(this,[t.event].concat(o()(t.args))):this.$emit(t.event);var e=this.$props.data,n=0!==this.controllers.length;n&&this.pre_dc(t),e.tv&&e.on_custom_event(t.event,t.args),n&&this.post_dc(t)},range_changed:function(t){if(this.chart_props.ib){var e=this.$refs.chart.ti_map;t=t.map((function(t){return e.i2t(t)}))}this.$emit("range-changed",t),this.custom_event({event:"range-changed",args:[t]}),this.onrange&&this.onrange(t)},set_loader:function(t){var e=this;this.onrange=function(n){var i=e.chart_props.ib?"_ms":"",r=e.$refs.chart["interval"+i];t.range_changed(n,r)}},parse_colors:function(t){for(var e in this.$props)if(0===e.indexOf("color")&&"colors"!==e){var n=e.replace("color","");if(t[n=n[0].toLowerCase()+n.slice(1)])continue;t[n]=this.$props[e]}},mousedown:function(){this.$refs.chart.activated=!0},mouseleave:function(){this.$refs.chart.activated=!1}}},Sn=(n(69),Z(En,i,[],!1,null,null,null));Sn.options.__file="src/TradingVue.vue";var Tn=Sn.exports,Mn=n(10),On=n.n(Mn),Qn=n(28),Dn=n.n(Qn),Gn=n(7),Yn=n.n(Gn),jn=n(11),Un=n.n(jn),Rn=n(33),Fn=n(34),Ln=n.n(Fn),Nn=(n(72),function(){function t(e){Y()(this,t),this.dc=e,this.tasks={},this.onevent=function(){},this.start()}var e,n;return U()(t,[{key:"start",value:function(){var t=this;this.worker&&this.worker.terminate(),window.URL=window.URL||window.webkitURL;var e,n=Ln.a.decompressFromBase64(Rn[0]);try{e=new Blob([n],{type:"application/javascript"})}catch(t){window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder,(e=new BlobBuilder).append(n),e=e.getBlob()}this.worker=new Worker(URL.createObjectURL(e)),this.worker.onmessage=function(e){return t.onmessage(e)}}},{key:"start_socket",value:function(){var t=this;this.dc.sett.node_url&&(this.socket=new WebSocket(this.dc.sett.node_url),this.socket.addEventListener("message",(function(e){t.onmessage({data:JSON.parse(e.data)})})),this.msg_queue=[])}},{key:"send",value:function(t,e){if(this.dc.sett.node_url)return this.send_node(t,e);if(e){var n=e.map((function(e){return t.data[e]}));this.worker.postMessage(t,n)}else this.worker.postMessage(t)}},{key:"send_node",value:function(t,e){if(this.socket||this.start_socket(),this.socket&&this.socket.readyState){for(;this.msg_queue.length;){var n=this.msg_queue.shift();this.socket.send(JSON.stringify(n))}this.socket.send(JSON.stringify(t))}else this.msg_queue.push(t)}},{key:"onmessage",value:function(t){t.data.id in this.tasks?(this.tasks[t.data.id](t.data.data),delete this.tasks[t.data.id]):this.onevent(t)}},{key:"exec",value:(n=Un()(Yn.a.mark((function t(e,n,i){var r=this;return Yn.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,o){var s=y.uuid();r.send({type:e,id:s,data:n},i),r.tasks[s]=function(e){t(e)}})));case 1:case"end":return t.stop()}}),t)}))),function(t,e,i){return n.apply(this,arguments)})},{key:"just",value:function(t,e,n){var i=y.uuid();this.send({type:t,id:i,data:e},n)}},{key:"relay",value:(e=Un()(Yn.a.mark((function t(e,n){var i=this;return Yn.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===n&&(n=!1),t.abrupt("return",new Promise((function(t,r){i.send(e,e.tx_keys),n||(i.tasks[e.id]=function(e){t(e)})})));case 2:case"end":return t.stop()}}),t)}))),function(t,n){return e.apply(this,arguments)})},{key:"destroy",value:function(){this.worker&&this.worker.terminate()}}]),t}());function Pn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Kn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Kn(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Kn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var Hn=function(){function t(e,n){Y()(this,t),this.type=n.type,this.id=n.id,this.dc=e,n.data&&(this.dc.ww.just("upload-data",Wt()({},this.id,n)),delete n.data);var i=Object.getPrototypeOf(this);Object.setPrototypeOf(n,i),Object.defineProperty(n,"dc",{get:function(){return e}})}var e;return U()(t,[{key:"set",value:function(t,e){void 0===e&&(e=!0),this.dc.ww.just("dataset-op",{id:this.id,type:"set",data:t,exec:e})}},{key:"update",value:function(t){this.dc.ww.just("update-data",Wt()({},this.id,t))}},{key:"merge",value:function(t,e){void 0===e&&(e=!0),this.dc.ww.just("dataset-op",{id:this.id,type:"mrg",data:t,exec:e})}},{key:"remove",value:function(t){void 0===t&&(t=!0),this.dc.del("datasets.".concat(this.id)),this.dc.ww.just("dataset-op",{id:this.id,type:"del",exec:t}),delete this.dc.dss[this.id]}},{key:"data",value:(e=Un()(Yn.a.mark((function t(){var e;return Yn.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.dc.ww.exec("get-dataset",this.id);case 2:if(e=t.sent){t.next=5;break}return t.abrupt("return");case 5:return t.abrupt("return",e.data);case 6:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})}],[{key:"watcher",value:function(e,n){var i,r=e.map((function(t){return t.id})),o=n.map((function(t){return t.id})),s=Pn(r);try{for(s.s();!(i=s.n()).done;){var a=i.value;if(!o.includes(a)){var c=e.filter((function(t){return t.id===a}))[0];this.dss[a]=new t(this,c)}}}catch(t){s.e(t)}finally{s.f()}var u,h=Pn(o);try{for(h.s();!(u=h.n()).done;){a=u.value;!r.includes(a)&&this.dss[a]&&this.dss[a].remove()}}catch(t){h.e(t)}finally{h.f()}}},{key:"make_tx",value:function(t,e){var n=t.data.chart.data,i={};return e.find((function(t){return"OHLCV"===t.type}))&&(i={ohlcv:n}),i}}]),t}();function zn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Wn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Wn(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function Wn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Jn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return $n(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return $n(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function $n(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Vn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=ne()(t);if(e){var r=ne()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return te()(this,n)}}var Xn=function(t){Zt()(i,t);var e,n=Vn(i);function i(){return Y()(this,i),n.apply(this,arguments)}return U()(i,[{key:"init_tvjs",value:function(t){var e=this;this.tv||(this.tv=t,this.init_data(),this.update_ids(),this.tv.$watch((function(){return e.get_by_query(".settings")}),(function(t,n){return e.on_settings(t,n)})),this.tv.$watch((function(){return e.get(".").map((function(t){return t.settings.$uuid}))}),(function(t,n){return e.on_ids_changed(t,n)})),this.tv.$watch((function(){return e.get("datasets")}),Hn.watcher.bind(this)))}},{key:"init_data",value:function(t){"chart"in this.data||this.tv.$set(this.data,"chart",{type:"Candles",data:this.data.ohlcv||[]}),"onchart"in this.data||this.tv.$set(this.data,"onchart",[]),"offchart"in this.data||this.tv.$set(this.data,"offchart",[]),this.data.chart.settings||this.tv.$set(this.data.chart,"settings",{}),delete this.data.ohlcv,"datasets"in this.data||this.tv.$set(this.data,"datasets",[]);var e,n=Jn(this.data.datasets);try{for(n.s();!(e=n.n()).done;){var i=e.value;this.dss||(this.dss={}),this.dss[i.id]=new Hn(this,i)}}catch(t){n.e(t)}finally{n.f()}}},{key:"range_changed",value:(e=Un()(Yn.a.mark((function t(e,n,i){var r,o,s=this;return Yn.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===i&&(i=!1),this.loader){t.next=3;break}return t.abrupt("return");case 3:if(this.loading){t.next=19;break}if(r=this.data.chart.data[0][0],!(e[0]<r)){t.next=19;break}return this.loading=!0,t.next=9,y.pause(250);case 9:if((e=e.slice())[0]=Math.floor(e[0]),e[1]=Math.floor(r),!(o=this.loader(e,n,(function(t){s.chunk_loaded(t)})))||!o.then){t.next=19;break}return t.t0=this,t.next=17,o;case 17:t.t1=t.sent,t.t0.chunk_loaded.call(t.t0,t.t1);case 19:i||(this.last_chunk=[e,n]);case 20:case"end":return t.stop()}}),t,this)}))),function(t,n,i){return e.apply(this,arguments)})},{key:"chunk_loaded",value:function(t){if(Array.isArray(t))this.merge("chart.data",t);else for(var e in t)this.merge(e,t[e]);this.loading=!1,this.last_chunk&&(this.range_changed.apply(this,o()(this.last_chunk).concat([!0])),this.last_chunk=null)}},{key:"update_ids",value:function(){this.data.chart.id="chart.".concat(this.data.chart.type);var t={};this.gldc={},this.dcgl={};var e,n=Jn(this.data.onchart);try{for(n.s();!(e=n.n()).done;){void 0===t[(c=e.value).type]&&(t[c.type]=0);var i=t[c.type]++;c.id="onchart.".concat(c.type).concat(i),c.name||(c.name=c.type+" ".concat(i)),c.settings||this.tv.$set(c,"settings",{}),this.gldc["g0_".concat(c.type,"_").concat(i)]=c.id,this.dcgl[c.id]="g0_".concat(c.type,"_").concat(i)}}catch(t){n.e(t)}finally{n.f()}t={};var r,o=[{}],s=0,a=Jn(this.data.offchart);try{for(a.s();!(r=a.n()).done;){var c;void 0===t[(c=r.value).type]&&(t[c.type]=0);var u=t[c.type]++;c.id="offchart.".concat(c.type).concat(u),c.name||(c.name=c.type+" ".concat(u)),c.settings||this.tv.$set(c,"settings",{}),s++;var h=(c.grid||{}).id||s;(c.grid||{}).id&&s--,o[h]||(o[h]={}),void 0===o[h][c.type]&&(o[h][c.type]=0);var l=o[h][c.type]++;this.gldc["g".concat(h,"_").concat(c.type,"_").concat(l)]=c.id,this.dcgl[c.id]="g".concat(h,"_").concat(c.type,"_").concat(l)}}catch(t){a.e(t)}finally{a.f()}}},{key:"update_candle",value:function(t){var e=this.data.chart.data,n=e[e.length-1],i=t.candle,r=this.tv.$refs.chart.interval_ms,s=n[0]+r,a=t.t||y.now(),c=a>=s?a-a%r:n[0];return i.length>=6?c=i[0]:i=[c].concat(o()(i)),this.agg.push("ohlcv",i),this.update_overlays(t,c,r),c>=s}},{key:"update_tick",value:function(t){var e=this.data.chart.data,n=e[e.length-1],i=t.price,r=t.volume||0,o=this.tv.$refs.chart.interval_ms;if(!o)return console.warn("Define the main timeframe");var s=t.t||y.now();n||(n=[s-s%o]);var a=n[0]+o,c=s>=a?s-s%o:n[0];if((c>=a||!e.length)&&void 0!==i){var u=[c,i,i,i,i,r];this.agg.push("ohlcv",u,o),e.push(u),this.scroll_to(c)}else void 0!==i&&(n[2]=Math.max(i,n[2]),n[3]=Math.min(i,n[3]),n[4]=i,n[5]+=r,this.agg.push("ohlcv",n,o));return this.update_overlays(t,c,o),c>=a}},{key:"update_overlays",value:function(t,e,n){for(var i in t)if("price"!==i&&"volume"!==i&&"candle"!==i&&"t"!==i)if(i.includes("datasets."))this.agg.push(i,t[i],n);else{if(Array.isArray(t[i]))r=t[i];else var r=[t[i]];i.includes(".data")||(i+=".data"),this.agg.push(i,[e].concat(o()(r)),n)}}},{key:"get_by_query",value:function(t,e){var n=t.split(".");switch(n[0]){case"chart":var i=this.chart_as_piv(n);break;case"onchart":case"offchart":i=this.query_search(t,n);break;case"datasets":var r,s=Jn(i=this.query_search(t,n));try{for(s.s();!(r=s.n()).done;){var a=r.value;"data"===a.i&&(a.v=this.dss[a.p.id].data())}}catch(t){s.e(t)}finally{s.f()}break;default:var c=this.query_search(t,["onchart",n[0],n[1]]),u=this.query_search(t,["offchart",n[0],n[1]]);i=[].concat(o()(c),o()(u))}return i.filter((function(t){return!(t.v||{}).locked||e}))}},{key:"chart_as_piv",value:function(t){var e=t[1];return e?[{p:this.data.chart,i:e,v:this.data.chart[e]}]:[{p:this.data,i:"chart",v:this.data.chart}]}},{key:"query_search",value:function(t,e){var n=this,i=e[0],r=e[1]||"",o=e[2],s=this.data[i].filter((function(e){return e.id===t||e.id&&e.id.includes(r)||e.name===t||e.name&&e.name.includes(r)||t.includes((e.settings||{}).$uuid)}));return o?s.map((function(t){return{p:t,i:o,v:t[o]}})):s.map((function(t,e){return{p:n.data[i],i:n.data[i].indexOf(t),v:t}}))}},{key:"merge_objects",value:function(t,e,n){void 0===n&&(n={}),Object.assign(n,t.v),Object.assign(n,e),this.tv.$set(t.p,t.i,n)}},{key:"merge_ts",value:function(t,e){if(!e.length)return t.v;var n=[t.v[0][0],t.v[t.v.length-1][0]],i=[e[0][0],e[e.length-1][0]],r=[Math.max(n[0],i[0]),Math.min(n[1],i[1])];if(r[1]>=r[0]){var s,a,c=this.ts_overlap(t.v,e,r),u=c.od,h=c.d1,l=c.d2;if((s=t.v).splice.apply(s,o()(h)),(a=e).splice.apply(a,o()(l)),!t.v.length&&!e.length)return this.tv.$set(t.p,t.i,u),t.v;e.length||(e=t.v.splice(h[0])),t.v.length||(t.v=e.splice(l[0])),this.tv.$set(t.p,t.i,this.combine(t.v,u,e))}else this.tv.$set(t.p,t.i,this.combine(t.v,[],e));return t.v}},{key:"ts_overlap",value:function(t,e,n){for(var i=n[0],r=n[1],o={},s=t.filter((function(t){return t[0]>=i&&t[0]<=r})),a=e.filter((function(t){return t[0]>=i&&t[0]<=r})),c=t.indexOf(s[0]),u=t.indexOf(s[s.length-1]),h=e.indexOf(a[0]),l=e.indexOf(a[a.length-1]),f=0;f<s.length;f++)o[s[f][0]]=s[f];for(f=0;f<a.length;f++)o[a[f][0]]=a[f];return{od:Object.keys(o).sort().map((function(t){return o[t]})),d1:[c,u-c+1],d2:[h,l-h+1]}}},{key:"combine",value:function(t,e,n){function i(t){return t[t.length-1][0]}return t.length||(t=e,e=[]),n.length||(n=e,e=[]),n[0][0]>=t[0][0]&&i(n)<=i(t)?Object.assign(t,e):i(n)>i(t)?e.length<1e5&&n.length<1e5?((r=t).push.apply(r,o()(e).concat(o()(n))),t):t.concat(e,n):n[0][0]<t[0][0]?e.length<1e5&&n.length<1e5?((s=n).push.apply(s,o()(e).concat(o()(t))),n):n.concat(e,t):[];var r,s}},{key:"fast_merge",value:function(t,e,n){if(void 0===n&&(n=!0),t){var i=(t[t.length-1]||[])[0],r=e[0];!t.length||r>i?(t.push(e),n&&this.sett.auto_scroll&&this.scroll_to(r)):r===i&&(n?this.tv.$set(t,t.length-1,e):t[t.length-1]=e)}}},{key:"scroll_to",value:function(t){if(!this.tv.$refs.chart.cursor.locked){var e=this.tv.$refs.chart.last_candle;if(e){var n=e[0],i=this.tv.getRange()[1]-n;i>0&&this.tv.goto(t+i)}}}}]),i}(function(){function t(){var e=this;Y()(this,t),this.ww=new Nn(this),this.ww.onevent=function(t){var n,i=zn(e.tv.controllers);try{for(i.s();!(n=i.n()).done;){(a=n.value).ww&&a.ww(t.data)}}catch(t){i.e(t)}finally{i.f()}switch(t.data.type){case"request-data":if(e.ww._data_uploading)break;var r=Hn.make_tx(e,t.data.data);e.send_meta_2_ww(),e.ww.just("upload-data",r),e.ww._data_uploading=!0;break;case"overlay-data":e.on_overlay_data(t.data.data);break;case"overlay-update":e.on_overlay_update(t.data.data);break;case"data-uploaded":e.ww._data_uploading=!1;break;case"engine-state":e.se_state=Object.assign(e.se_state||{},t.data.data);break;case"modify-overlay":e.modify_overlay(t.data.data);break;case"script-signal":e.tv.$emit("signal",t.data.data)}var o,s=zn(e.tv.controllers);try{for(s.s();!(o=s.n()).done;){var a;(a=o.value).post_ww&&a.post_ww(t.data)}}catch(t){s.e(t)}finally{s.f()}}}return U()(t,[{key:"on_custom_event",value:function(t,e){switch(t){case"register-tools":this.register_tools(e);break;case"exec-script":this.exec_script(e);break;case"exec-all-scripts":this.exec_all_scripts();break;case"data-len-changed":this.data_changed(e);break;case"tool-selected":if(!e[0])break;if("System"===e[0].split(":")[0]){this.system_tool(e[0].split(":")[1]);break}this.tv.$set(this.data,"tool",e[0]),"Cursor"===e[0]&&this.drawing_mode_off();break;case"grid-mousedown":this.grid_mousedown(e);break;case"drawing-mode-off":this.drawing_mode_off();break;case"change-settings":this.change_settings(e);break;case"range-changed":this.scripts_onrange.apply(this,o()(e));break;case"scroll-lock":this.on_scroll_lock(e[0]);break;case"object-selected":this.object_selected(e);break;case"remove-tool":this.system_tool("Remove");break;case"before-destroy":this.before_destroy()}}},{key:"on_settings",value:function(t,e){var n=this;if(this.sett.scripts){for(var i={},r=!1,o=function(){var o=t[s];if(!e.filter((function(t){return t.v===o.v})).length&&o.p.settings.$props){var a=o.p.settings.$uuid;y.is_scr_props_upd(o,e)&&y.delayed_exec(o.p)&&(i[a]=o.v,r=!0,n.tv.$set(o.p,"loading",!0))}},s=0;s<t.length;s++)o();if(r&&Object.keys(i).length){var a=this.tv.$refs.chart.interval_ms||this.data.chart.tf,c=this.tv.getRange();this.ww.just("update-ov-settings",{delta:i,tf:a,range:c})}}}},{key:"on_ids_changed",value:function(t,e){var n=e.filter((function(e){return void 0!==e&&!t.includes(e)}));n.length&&this.ww.just("remove-scripts",n)}},{key:"register_tools",value:function(t){var e,n={},i=zn(this.data.tools||[]);try{for(i.s();!(e=i.n()).done;){n[(a=e.value).type]=a,delete a.type}}catch(t){i.e(t)}finally{i.f()}this.tv.$set(this.data,"tools",[]);var r,o=[{type:"Cursor",icon:Ht["cursor.png"]}],s=zn(t);try{for(s.s();!(r=s.n()).done;){var a=r.value,c=Object.assign({},a.info),u=a.info.type||"Default";for(var h in c.type="".concat(a.use_for,":").concat(u),this.merge_presets(c,n[a.use_for]),this.merge_presets(c,n[c.type]),delete c.mods,o.push(c),a.info.mods){var l=Object.assign({},c);(l=Object.assign(l,a.info.mods[h])).type="".concat(a.use_for,":").concat(h),this.merge_presets(l,n[a.use_for]),this.merge_presets(l,n[l.type]),o.push(l)}}}catch(t){s.e(t)}finally{s.f()}this.tv.$set(this.data,"tools",o),this.tv.$set(this.data,"tool","Cursor")}},{key:"exec_script",value:function(t){if(t.length&&this.sett.scripts){var e=this.get_overlay(t[0]);if(!e||!1===e.scripts)return;var n=e.settings,i=t[0].src.props||{};for(var r in n.$uuid||(n.$uuid="".concat(e.type,"-").concat(y.uuid2())),t[0].uuid=n.$uuid,t[0].sett=n,i||{}){var o=i[r];if(void 0===n[r]){if(void 0===o.def)return void console.error("Overlay ".concat(e.id,": script prop '").concat(r,"' ")+"doesn't have a default value");n[r]=o.val=o.def}else o.val=n[r]}if(n.$props)for(var r in n)n.$props.includes(r)&&!(r in i)&&delete n[r];n.$props=Object.keys(t[0].src.props||{}),this.tv.$set(e,"loading",!0);var s=this.tv.$refs.chart.interval_ms||this.data.chart.tf,a=this.tv.getRange();e.script&&null!=e.script.output&&(t[0].output=e.script.output),this.ww.just("exec-script",{s:t[0],tf:s,range:a})}}},{key:"exec_all_scripts",value:function(){if(this.sett.scripts){this.set_loading(!0);var t=this.tv.$refs.chart.interval_ms||this.data.chart.tf,e=this.tv.getRange();this.ww.just("exec-all-scripts",{tf:t,range:e})}}},{key:"scripts_onrange",value:function(t){if(this.sett.scripts){var e={};if(this.get(".").forEach((function(t){t.script&&t.script.execOnRange&&t.settings.$uuid&&y.delayed_exec(t)&&(e[t.settings.$uuid]=t.settings)})),Object.keys(e).length){var n=this.tv.$refs.chart.interval_ms||this.data.chart.tf,i=this.tv.getRange();this.ww.just("update-ov-settings",{delta:e,tf:n,range:i})}}}},{key:"modify_overlay",value:function(t){var e=this.get_overlay(t);if(e)for(var n in t.fields||{})"object"===On()(e[n])?this.merge("".concat(t.uuid,".").concat(n),t.fields[n]):this.tv.$set(e,n,t.fields[n])}},{key:"data_changed",value:function(t){if(this.sett.scripts&&!1!==this.sett.data_change_exec){var e=this.data.chart.data;this.ww._data_uploading||this.se_state.scripts&&(this.send_meta_2_ww(),this.ww.just("upload-data",{ohlcv:e}),this.ww._data_uploading=!0,this.set_loading(!0))}}},{key:"set_loading",value:function(t){var e,n=zn(this.get(".").filter((function(t){return t.settings.$props})));try{for(n.s();!(e=n.n()).done;){var i=e.value;this.merge("".concat(i.id),{loading:t})}}catch(t){n.e(t)}finally{n.f()}}},{key:"send_meta_2_ww",value:function(){var t=this.tv.$refs.chart.interval_ms||this.data.chart.tf,e=this.tv.getRange();this.ww.just("send-meta-info",{tf:t,range:e})}},{key:"merge_presets",value:function(t,e){if(e)for(var n in e)"settings"===n?Object.assign(t[n],e[n]):t[n]=e[n]}},{key:"grid_mousedown",value:function(t){var e=this;this.object_selected([]);var n=function(){return e.get("RangeTool").filter((function(t){return t.settings.shiftMode})).forEach((function(t){return e.del(t.id)}))};this.data.tool&&"Cursor"!==this.data.tool&&!this.data.drawingMode?"tap"!==t[1].type?(this.tv.$set(this.data,"drawingMode",!0),this.build_tool(t[0])):this.tv.showTheTip("<b>Hodl</b>+<b>Drug</b> to create, <b>Tap</b> to finish a tool"):this.sett.shift_measure&&t[1].shiftKey?(n(),this.tv.$nextTick((function(){return e.build_tool(t[0],"RangeTool:ShiftMode")}))):n()}},{key:"drawing_mode_off",value:function(){this.tv.$set(this.data,"drawingMode",!1),this.tv.$set(this.data,"tool","Cursor")}},{key:"build_tool",value:function(t,e){var n=this.data.tools;e=e||this.data.tool;var i=n.find((function(t){return t.type===e}));if(i){var r=Object.assign({},i.settings||{}),o=(i.data||[]).slice();"legend"in r||(r.legend=!1),"z-index"in r||(r["z-index"]=100),r.$selected=!0,r.$state="wip";var s=t?"offchart":"onchart",a=this.add(s,{name:i.name,type:e.split(":")[0],settings:r,data:o,grid:{id:t}});r.$uuid="".concat(a,"-").concat(y.now()),this.tv.$set(this.data,"selected",r.$uuid),this.add_trash_icon()}}},{key:"system_tool",value:function(t){switch(t){case"Remove":this.data.selected&&(this.del(this.data.selected),this.remove_trash_icon(),this.drawing_mode_off(),this.on_scroll_lock(!1))}}},{key:"change_settings",value:function(t){var e=t[0];delete e.id;t[1];this.merge("".concat(t[3],".settings"),e)}},{key:"on_scroll_lock",value:function(t){this.tv.$set(this.data,"scrollLock",t)}},{key:"object_selected",value:function(t){var e=this.data.selected;e&&(this.merge("".concat(e,".settings"),{$selected:!1}),this.remove_trash_icon()),this.tv.$set(this.data,"selected",null),t.length&&(this.tv.$set(this.data,"selected",t[2]),this.merge("".concat(t[2],".settings"),{$selected:!0}),this.add_trash_icon())}},{key:"add_trash_icon",value:function(){var t="System:Remove";this.data.tools.find((function(e){return e.type===t}))||this.data.tools.push({type:t,icon:Ht["trash.png"]})}},{key:"remove_trash_icon",value:function(){y.overwrite(this.data.tools,this.data.tools.filter((function(t){return"System:Remove"!==t.type})))}},{key:"on_overlay_data",value:function(t){var e=this;this.get(".").forEach((function(t){t.settings.$synth&&e.del("".concat(t.id))}));var n,i=zn(t);try{for(i.s();!(n=i.n()).done;){var r=n.value,o=this.get_one("".concat(r.id));if(o){if(this.tv.$set(o,"loading",!1),!r.data)continue;o.data=r.data}if(r.new_ovs){for(var s in r.new_ovs.onchart)this.get_one("onchart.".concat(s))||this.add("onchart",r.new_ovs.onchart[s]);for(var s in r.new_ovs.offchart)this.get_one("offchart.".concat(s))||this.add("offchart",r.new_ovs.offchart[s])}}}catch(t){i.e(t)}finally{i.f()}}},{key:"on_overlay_update",value:function(t){var e,n=zn(t);try{for(n.s();!(e=n.n()).done;){var i=e.value;if(i.data){var r=this.get_one("".concat(i.id));r&&this.fast_merge(r.data,i.data,!1)}}}catch(t){n.e(t)}finally{n.f()}}},{key:"before_destroy",value:function(){var t=function(t){return!t.settings.$state||"finished"===t.settings.$state};this.data.onchart=this.data.onchart.filter(t),this.data.offchart=this.data.offchart.filter(t),this.drawing_mode_off(),this.on_scroll_lock(!1),this.object_selected([]),this.ww.destroy()}},{key:"get_overlay",value:function(t){var e=t.id||"g".concat(t.grid_id,"_").concat(t.layer_id),n=t.uuid||this.gldc[e];return this.get_one("".concat(n))}}]),t}()),Zn=function(){function t(e,n){void 0===n&&(n=100),Y()(this,t),this.symbols={},this.int=n,this.dc=e,this.st_id=null,this.data_changed=!1}return U()(t,[{key:"push",value:function(t,e,n){var i=this;this.st_id||(this.st_id=setTimeout((function(){return i.update()}))),n=parseInt(n);var r=this.symbols[t],o=y.now(),s=t.includes("datasets.");this.data_changed=!0,r?e[0]>=r.upd[0]+n&&!s?(this.refine(t,r.upd.slice()),this.symbols[t]={upd:e,t:o,data:[]}):(this.symbols[t].upd=e,this.symbols[t].t=o):this.symbols[t]={upd:e,t:o,data:[]},s&&this.symbols[t].data.push(e)}},{key:"update",value:function(){var t=this,e={};for(var n in this.symbols){var i=this.symbols[n].upd;switch(n){case"ohlcv":var r=this.dc.data.chart.data;this.dc.fast_merge(r,i),e.ohlcv=r.slice(-2);break;default:if(n.includes("datasets.")){this.update_ds(n,e);continue}if(!(r=this.dc.get_one("".concat(n))))continue;this.dc.fast_merge(r,i,!1)}}this.data_changed&&(this.dc.ww.just("update-data",e),this.data_changed=!1),setTimeout((function(){return t.update()}),this.int)}},{key:"refine",value:function(t,e){if("ohlcv"===t){var n=this.dc.data.chart.data;this.dc.fast_merge(n,e)}else{if(!(n=this.dc.get_one("".concat(t))))return;this.dc.fast_merge(n,e,!1)}}},{key:"update_ds",value:function(t,e){var n=this.symbols[t].data;n.length&&(e[t]=n,this.symbols[t].data=[])}},{key:"clear",value:function(){this.symbols={}}}]),t}();function qn(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return ti(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ti(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function ti(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function ei(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=ne()(t);if(e){var r=ne()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return te()(this,n)}}var ni=function(t){Zt()(n,t);var e=ei(n);function n(t,i){var r;void 0===t&&(t={}),void 0===i&&(i={}),Y()(this,n);return i=Object.assign({aggregation:100,script_depth:0,auto_scroll:!0,scripts:!0,ww_ram_limit:0,node_url:null,shift_measure:!0},i),(r=e.call(this)).sett=i,r.data=t,r.sett=function(t,e){var n={get:function(t,e){return t[e]},set:function(t,n,i){return t[n]=i,e.just("update-dc-settings",t),!0}};return e.just("update-dc-settings",t),new Proxy(t,n)}(i,r.ww),r.agg=new Zn(Dn()(r),i.aggregation),r.se_state={},r}return U()(n,[{key:"add",value:function(t,e){if("onchart"===t||"offchart"===t||"datasets"===t)return this.data[t].push(e),this.update_ids(),e.id}},{key:"get",value:function(t){return this.get_by_query(t).map((function(t){return t.v}))}},{key:"get_one",value:function(t){return this.get_by_query(t).map((function(t){return t.v}))[0]}},{key:"set",value:function(t,e){var n,i=qn(this.get_by_query(t));try{for(i.s();!(n=i.n()).done;){var r=n.value,o=void 0!==r.i?r.i:r.p.indexOf(r.v);-1!==o&&this.tv.$set(r.p,o,e)}}catch(t){i.e(t)}finally{i.f()}this.update_ids()}},{key:"merge",value:function(t,e){var n,i=qn(this.get_by_query(t));try{for(i.s();!(n=i.n()).done;){var r=n.value;if(Array.isArray(r.v)){if(!Array.isArray(e))continue;r.v[0]&&r.v[0].length>=2?this.merge_ts(r,e):this.merge_objects(r,e,[])}else"object"===On()(r.v)&&this.merge_objects(r,e)}}catch(t){i.e(t)}finally{i.f()}this.update_ids()}},{key:"del",value:function(t){var e,n=qn(this.get_by_query(t));try{for(n.s();!(e=n.n()).done;){var i=e.value,r="number"!=typeof i.i?i.i:i.p.indexOf(i.v);-1!==r&&this.tv.$delete(i.p,r)}}catch(t){n.e(t)}finally{n.f()}this.update_ids()}},{key:"update",value:function(t){return t.candle?this.update_candle(t):this.update_tick(t)}},{key:"lock",value:function(t){this.get_by_query(t).forEach((function(t){t.v&&t.v.id&&t.v.type&&(t.v.locked=!0)}))}},{key:"unlock",value:function(t){this.get_by_query(t,!0).forEach((function(t){t.v&&t.v.id&&t.v.type&&(t.v.locked=!1)}))}},{key:"show",value:function(t){"offchart"===t||"onchart"===t?t+=".":"."===t&&(t=""),this.merge(t+".settings",{display:!0})}},{key:"hide",value:function(t){"offchart"===t||"onchart"===t?t+=".":"."===t&&(t=""),this.merge(t+".settings",{display:!1})}},{key:"onrange",value:function(t){var e=this;this.loader=t,setTimeout((function(){return e.tv.set_loader(t?e:null)}),0)}}]),n}(Xn),ii={props:["ux","updater","colors","wrapper"],mounted:function(){this._$emit=this.$emit,this.$emit=this.custom_event,this.init&&this.init()},methods:{close:function(){this.$emit("custom-event",{event:"close-interface",args:[this.$props.ux.uuid]})},modify:function(t){this.$emit("custom-event",{event:"modify-interface",args:[this.$props.ux.uuid,t]})},custom_event:function(t){if("hook"!==t.split(":")[0]){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];this._$emit("custom-event",{event:t,args:n})}}},computed:{overlay:function(){return this.$props.ux.overlay},layout:function(){return this.overlay.layout},uxr:function(){return this.$props.ux}},data:function(){return{}}},ri={Candle:Et,Volbar:St,Line:Vt,Pin:Jt,Price:Tt,Ray:re,Seg:$t};Tn.install=function(t){t.component(Tn.name,Tn)},"undefined"!=typeof window&&window.Vue&&(window.Vue.use(Tn),window.TradingVueLib={TradingVue:Tn,Overlay:ft,Utils:y,Constants:l,Candle:Et,Volbar:St,layout_cnv:Ct,layout_vol:It,DataCube:ni,Tool:Kt,Interface:ii,primitives:ri});e.default=Tn}])}));
//# sourceMappingURL=trading-vue.min.js.map