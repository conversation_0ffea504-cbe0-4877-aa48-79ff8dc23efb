package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableDO;
import com.pugwoo.branch.database.service.DatabaseStdWideTableService;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

@RestController
@RequestMapping(value = "/database_std_wide_table")
public class DatabaseStdWideTableController {

    @Autowired
    private DatabaseStdWideTableService databaseStdWideTableService;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("database/std_table/database_std_wide_table");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize) {
        PageData<DatabaseStdWideTableDO> pageData = databaseStdWideTableService.getPage(page, pageSize);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(DatabaseStdWideTableDO databaseStdWideTableDO) {
        WebCheckUtils.assertNotNull(databaseStdWideTableDO, "缺少修改的对象参数");

        // TODO check parameters

        ResultBean<Long> result = databaseStdWideTableService.insertOrUpdate(databaseStdWideTableDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(databaseStdWideTableService.deleteById(id));
    }

}
