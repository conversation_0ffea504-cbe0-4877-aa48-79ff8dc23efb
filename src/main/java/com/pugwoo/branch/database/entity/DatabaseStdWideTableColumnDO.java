package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 标准宽表列
 */
@Data
@ToString
@Table("database_std_wide_table_column")
public class DatabaseStdWideTableColumnDO extends AdminCoreDO {

    /** 表id<br/>Column: [table_id] */
    @Column(value = "table_id")
    private Long tableId;

    /** 列名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 表中的列名<br/>Column: [column_name] */
    @Column(value = "column_name")
    private String columnName;

    /** 列的数据类型<br/>Column: [column_type] */
    @Column(value = "column_type")
    private String columnType;

    /** 是否是最细的维度列<br/>Column: [is_uniq_dim] */
    @Column(value = "is_uniq_dim")
    private Boolean isUniqDim;

    /** 该类是否是枚举列，当是枚举列时，必须完善枚举说明<br/>Column: [is_enum] */
    @Column(value = "is_enum")
    private Boolean isEnum;

    /** 这一列的值个数<br/>Column: [distinct_value_count] */
    @Column(value = "distinct_value_count")
    private Integer distinctValueCount;

    /** 该列是否有null值<br/>Column: [is_contain_null] */
    @Column(value = "is_contain_null")
    private Boolean isContainNull;

    /** 最小值，只存前255个字符<br/>Column: [min_value] */
    @Column(value = "min_value")
    private String minValue;

    /** 最大值，只存前255个字符<br/>Column: [max_value] */
    @Column(value = "max_value")
    private String maxValue;

    /** 列说明<br/>Column: [note] */
    @Column(value = "note")
    private String note;

}