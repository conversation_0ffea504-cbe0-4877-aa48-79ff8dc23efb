package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnEnumDO;
import com.pugwoo.branch.database.service.DatabaseStdWideTableColumnEnumService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DatabaseStdWideTableColumnEnumServiceImpl implements DatabaseStdWideTableColumnEnumService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public DatabaseStdWideTableColumnEnumDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DatabaseStdWideTableColumnEnumDO.class, id);
    }

    @Override
    public PageData<DatabaseStdWideTableColumnEnumDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseStdWideTableColumnEnumDO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseStdWideTableColumnEnumDO databaseStdWideTableColumnEnumDO) {
        if(databaseStdWideTableColumnEnumDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(databaseStdWideTableColumnEnumDO);
        return rows > 0 ? ResultBean.ok(databaseStdWideTableColumnEnumDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DatabaseStdWideTableColumnEnumDO databaseStdWideTableColumnEnumDO = new DatabaseStdWideTableColumnEnumDO();
        databaseStdWideTableColumnEnumDO.setId(id);
        return dbHelper.delete(databaseStdWideTableColumnEnumDO) > 0;
    }

}