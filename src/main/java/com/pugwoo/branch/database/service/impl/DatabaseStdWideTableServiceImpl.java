package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableDO;
import com.pugwoo.branch.database.service.DatabaseStdWideTableService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DatabaseStdWideTableServiceImpl implements DatabaseStdWideTableService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public DatabaseStdWideTableDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DatabaseStdWideTableDO.class, id);
    }

    @Override
    public PageData<DatabaseStdWideTableDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseStdWideTableDO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseStdWideTableDO databaseStdWideTableDO) {
        if(databaseStdWideTableDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(databaseStdWideTableDO);
        return rows > 0 ? ResultBean.ok(databaseStdWideTableDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DatabaseStdWideTableDO databaseStdWideTableDO = new DatabaseStdWideTableDO();
        databaseStdWideTableDO.setId(id);
        return dbHelper.delete(databaseStdWideTableDO) > 0;
    }

}