package com.pugwoo.branch.chart.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;

import java.util.List;
import java.util.Objects;

/**
 * 图表类型枚举，所有支持的，能用的图表都放这里
 */
@Getter
public enum ChartEnum {

    BAR_3D("BAR_3D", "3D柱状图", "echarts",
            ListUtils.of("3dbar", "bar3d", "3d_bar", "bar_3d")),

    LINES("LINES", "多折线图", "echarts",
            ListUtils.of("lines", "line")),

    PERCENTAGE_AREA("PERCENTAGE_AREA", "百分比面积图", "highcharts",
            ListUtils.of("percentage_area", "area_percentage", "percent_area", "area_percent")),

    PIE("PIE", "饼图", "echarts", ListUtils.of("pie")),

    BUBBLE("BUBBLE", "气泡图", "echarts", ListUtils.of("bubble")),

    K_LINE("K_LINE", "K线图", "tradingvue",
            ListUtils.of("kline", "kLine", "k_line", "k-line")),

    ;

    final private String code;
    final private String name;
    /**
     * 图表库，如echarts，highcharts等，忽略大小写
     */
    final private String library;
    /**
     * 图表名称，如bar3d等，支持设置多个，更方便用户填写，忽略大小写
     */
    final private List<String> charts;

    ChartEnum(String code, String name, String library, List<String> charts) {
        this.code = code;
        this.name = name;
        this.library = library;
        this.charts = charts;
    }

    public static ChartEnum getByLibraryChart(String library, String chart) {
        for (ChartEnum e : ChartEnum.values()) {
            if (e.getLibrary().equalsIgnoreCase(library)) {
                for (String c : e.getCharts()) {
                    if (c.equalsIgnoreCase(chart)) {
                        return e;
                    }
                }
            }
        }
        return null;
    }


    public static ChartEnum getByCode(String code) {
        for (ChartEnum e : ChartEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ChartEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}