package com.pugwoo.branch.chart.service.impl;

import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.tradingvue.CreateTradeDTO;
import com.pugwoo.branch.chart.service.TradingVueService;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.common.VelocityTools;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class TradingVueServiceImpl implements TradingVueService {

    @Override
    public HtmlCssJsDTO createChart(CreateTradeDTO dto) {
        if (dto == null) {
            return null;
        }

        HtmlCssJsDTO htmlCssJsDTO = new HtmlCssJsDTO();

        // 准备模板参数
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle() != null ? dto.getTitle() : "K线图");

        // 将OHLCV数据转换为JavaScript数组格式
        String ohlcvDataArray = ChartCommonUtils.makeArray(dto.getOhlcvData());
        params.put("OHLCV_DATA", ohlcvDataArray);

        // 渲染模板
        String html = VelocityTools.renderVM("/chart/tradingvue/common.vm.html", params);
        htmlCssJsDTO.setHtml(html);
        htmlCssJsDTO.setCss("");
        htmlCssJsDTO.setJs("");

        return htmlCssJsDTO;
    }

}
