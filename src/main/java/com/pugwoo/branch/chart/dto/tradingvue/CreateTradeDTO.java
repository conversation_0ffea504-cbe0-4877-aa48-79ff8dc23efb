package com.pugwoo.branch.chart.dto.tradingvue;

import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import lombok.SneakyThrows;

import javax.swing.plaf.ListUI;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Data
public class CreateTradeDTO {

    /**
     * 图表标题
     */
    private String title;

    /**
     * OHLCV数据：[时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量]
     */
    private List<List<Object>> ohlcvData = new ArrayList<>();

    public static CreateTradeDTO from(CreateSandboxReq req) {
        CreateTradeDTO dto = new CreateTradeDTO();
        dto.setTitle(req.getName() != null ? req.getName() : "K线图");

        if (req.getData() != null && !req.getData().isEmpty()) {
            List<List<Object>> ohlcvData = new ArrayList<>();

            for (List<String> row : req.getData()) {
                if (row.size() >= 6) {
                    List<Object> ohlcvRow = new ArrayList<>();

                    // 转换日期格式：从20250508转换为时间戳
                    String dateStr = row.get(0);
                    long timestamp = convertDateToTimestamp(dateStr);
                    ohlcvRow.add(timestamp);

                    // 添加开盘价、最高价、最低价、收盘价、成交量
                    for (int i = 1; i < 6; i++) {
                        BigDecimal value = NumberUtils.parseBigDecimal(row.get(i));
                        ohlcvRow.add(value != null ? value : BigDecimal.ZERO);
                    }

                    ohlcvData.add(ohlcvRow);
                }
            }

            // ohlcvData时间要正序排列
            ListUtils.sortAscNullLast(ohlcvData, o -> (long) o.getFirst());

            dto.setOhlcvData(ohlcvData);
        }

        return dto;
    }

    /**
     * 将日期字符串转换为时间戳(毫秒)
     * @param dateStr 格式如：20250508
     * @return 时间戳（毫秒）
     */
    @SneakyThrows
    private static long convertDateToTimestamp(String dateStr) {
        LocalDate date = DateUtils.parseLocalDateThrowException(dateStr);
        return date.atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli();
    }
}
